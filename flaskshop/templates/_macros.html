{% macro pagination_widget(pagination, endpoint, fragment='') %}
    <div class="pagination">
        <ul class="pagination">
            <li {% if not pagination.has_prev %} class="disabled" {% endif %}>
                <a href="





                        {% if pagination.has_prev %}{{ url_for(endpoint, page=pagination.prev_num, **kwargs) }}{{ fragment }}{% else %}#{% endif %}">
                    &laquo;
                </a>
            </li>
            {% for p in pagination.iter_pages() %}
                {% if p %}
                    {% if p == pagination.page %}
                        <li class="active">
                            <a href="{{ url_for(endpoint, page = p, **kwargs) }}{{ fragment }}">{{ p }}</a>
                        </li>
                    {% else %}
                        <li>
                            <a href="{{ url_for(endpoint, page = p, **kwargs) }}{{ fragment }}">{{ p }}</a>
                        </li>
                    {% endif %}
                {% else %}
                    <li class="disabled"><a href="#">&hellip;</a></li>
                {% endif %}
            {% endfor %}
            <li {% if not pagination.has_next %} class="disabled" {% endif %}>
                <a href="





                        {% if pagination.has_next %}{{ url_for(endpoint, page=pagination.next_num, **kwargs) }}{{ fragment }}{% else %}#{% endif %}">
                    &raquo;
                </a>
            </li>
        </ul>
    </div>
{% endmacro %}

{% macro menu(menu_items, horizontal=true) %}
    <ul class="menu {% if horizontal %}nav mb-4 mb-md-0{% endif %}">
        {% for item in menu_items %}
            {% with children=item.children %}
                <li class="{% if horizontal %}nav-item{% endif %} {% if children %}nav-item__dropdown{% endif %} menu__item">
                    <a class="{% if horizontal %}nav-link{% endif %}" href="{{ item.url }}">
                        {{ item }}
                    </a>
                    {% if children %}
                        <div class="{% if horizontal %}nav-item__dropdown-content{% else %}nav-item__submenu{% endif %}">
                            <div class="container">
                                <ul>
                                    {% for child in children %}
                                        <li>
                                            <a href="{{ child.url }}">
                                                {% if horizontal %}
                                                    <strong>{{ child }}</strong>
                                                {% else %}
                                                    {{ child }}
                                                {% endif %}
                                            </a>
                                        </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                    {% endif %}
                </li>
            {% endwith %}
        {% endfor %}
    </ul>
{% endmacro %}

{% macro format_address(address) %}
    {{ address.province }}-{{ address.city }}-{{ address.district }}<br>
    {{ address.address }}<br>
    {{ address.contact_name }}<br>
    {{ address.contact_phone }}<br>
{% endmacro %}

{% macro footer_menu(menu_items) %}
    {% for item in menu_items %}
        {% with children=item.children %}
            <div class="col-6 col-md-2">
                <ul class="menu">
                    <li class="nav-item__dropdown menu__item">
                        <a>
                            <strong>{{ item.title }}</strong>
                        </a>
                        <hr/>
                    </li>
                    {% for child in children %}
                        <li>
                            <a rel="nofollow" href="{{ child.url }}">
                                {{ child.title }}
                            </a>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        {% endwith %}
    {% endfor %}
{% endmacro %}

{% macro navlink(endpoint, name, active='') %}
    <li class="list-group-item {% if endpoint == request.endpoint or endpoint == active or active == True %}active{% endif %}">
        <a href="{{ url_for(endpoint) }}">{{ name }}</a>
    </li>
{% endmacro %}
