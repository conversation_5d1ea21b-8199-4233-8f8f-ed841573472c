<div class="container">
  {% if current_user.is_authenticated and current_user_cart and current_user_cart.quantity > 0 %}
  <div id="cart-dropdown-list"
    class="row cart-dropdown__list{% if current_user_cart.lines|length <= 2 %} overflow{% endif %}">
    {% for line in current_user_cart.lines %}
    <div class="row item">
      <div class="col-md-10">
        <a class="link--clean" href="{{ line.product.get_absolute_url() }}">
          <img class="cart-dropdown__image lazyload lazypreload" alt="" src="{{ line.product.first_img }}">
          <h3 class="col-md-11">
            {{ line.product }}
            <span>x{{ line.quantity }}</span>
            <p>{{ line.variant }}</p>
          </h3>
        </a>
      </div>
      <div class="col-md-2">
        <div class="float-right">
          <h3>
            ${{ line.variant.price }}
          </h3>
        </div>
      </div>
    </div>
    {% endfor %}
  </div>
  <div class="row cart-dropdown__total" data-quantity="{{ quantity }}">
    <div class="col-md-8">
      <h3>
        Subtotal<br>
        <span>{% trans %}Shipment calculated at checkout{% endtrans %}</span>
      </h3>
    </div>
    <div class="col-md-4">
      <h3 class="float-md-right price {% if current_user_cart.lines|length <= 2 %}single{% endif %}"
        data-quantity="{{ quantity }}">
        <p>
          ${{ current_user_cart.total }}
        </p>
      </h3>
    </div>
  </div>
  <div class="row cart-dropdown__actions">
    <div class="col-md-1"></div>
    <div class="col-md-5">
      <a href="{{ url_for('checkout.cart_index') }}" class="btn secondary narrow float-md-right">Go to cart</a>
    </div>
    <div class="col-md-5">
      <a href="{% if current_user_cart.is_shipping_required %}{{ url_for('checkout.checkout_shipping') }}{% else %}{{ url_for('checkout.checkout_note') }}{% endif %}"
        class="btn btn-primary narrow float-md-right">{% trans %}Checkout{% endtrans %}</a>
    </div>
  </div>
  {% else %}
  <div class="text-md-center cart-dropdown__empty">
    <h3>{% trans %}There are no products in your shopping cart.{% endtrans %}</h3>
    <img src="{{ url_for('static', filename='img/empty-cart-bg.png') }}" class="lazyload lazypreload" title="empty">
    <a href="" class="btn btn-primary">{% trans %}Check out our sales{% endtrans %}</a>
  </div>
  {% endif %}
</div>
