{% extends "checkout/details.html" %}
{% from 'bootstrap5/form.html' import render_field %}

{% block forms %}
{% if address %}
<div class="row">
  <div class="col-md-6">
    <h3>{% trans %}Shipping address{% endtrans %}</h3>
    {{ macros.format_address(address) }}
    <p><a href="{{ url_for('checkout.checkout_shipping') }}">{% trans %}Select other address{% endtrans %}</a></p>
  </div>
  <div class="col-md-6">
    <h3>Shipping Method</h3>
    {{ shipping_method }}
    <p><a href="{{ url_for('checkout.checkout_shipping') }}">{% trans %}Select other shipping method{% endtrans %}</a>
    </p>
  </div>
</div>
<hr>
{% endif %}


<form method="post" novalidate>
  {{ form.csrf_token }}
  {{ render_field(form.note) }}
  <p class="text-md-right">
    <button type="submit" class="btn btn-primary">
      ORDER & PAY
    </button>
  </p>
</form>
{% endblock %}
