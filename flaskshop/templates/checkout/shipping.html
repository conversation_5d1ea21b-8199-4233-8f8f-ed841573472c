{% extends "checkout/details.html" %}
{% import '_macros.html' as macros %}
{% block forms %}
<form method="post" novalidate class="checkout__shipping">
  <div class="row">
    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
      <div class="mb-1">
        <h2>{% trans %}Shipping address{% endtrans %}</h2>
      </div>

      {% for address in current_user.addresses %}
      <div class="radio address_hide">
        <label>
          <input type="radio" name="address_sel" value="{{ address.id }}" id="address_{{ address.id }}"
            {% if loop.first %}checked{%endif%} />
          <div class="address-details">
            {{ macros.format_address(address) }}
          </div>
        </label>
      </div>
      {% endfor %}

      <div class="radio address_show col-12 float-left">
        <label>
          <input type="radio" name="address_sel" value="new" id="address_new">
          {% trans %}Enter a new address{% endtrans %}
        </label>
      </div>


      <div class="checkout__new-address">
        <div class="address_show col-12 float-left">
          {% include "account/partials/address_form.html" %}
        </div>
      </div>
    </div>
  </div>
  <div class="row mt-3">
    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
      <div class="mb-1">
        <h2>{% trans %}Shipping method{% endtrans %}</h2>
      </div>
      {% for method in shipping_methods %}
      <div class="radio col-6 float-left">
        <label>
          <input type="radio" name="shipping_method" value="{{ method.id }}" id="shipping_{{ method.id }}"
            {% if loop.first %}checked{%endif%} />
          <div class="address-details">
            {{ method }}
          </div>
        </label>
      </div>
      {% endfor %}
    </div>
  </div>
  <p class="text-md-right">
    <button type="submit" class="btn btn-primary">
      {% trans %}Continue{% endtrans %}
    </button>
  </p>
</form>
{% endblock %}
