{% extends "base.html" %}

{% block title %}{% trans %}Checkout{% endtrans %}{% endblock %}

{% block header %}
<div class="container-fluid navbar__login">
  <div class="container">
    <p>{% trans %}Easy and secure!{% endtrans %}</p>
  </div>
</div>
<header class="checkout__header">
  <div class="container">
    <a href="{{ url_for('public.home') }}">
      <svg data-src="{{ url_for('static', filename='img/logo-document.svg') }}" height="48px" width="176px"></svg>
    </a>
    <h1>{% trans %}Checkout{% endtrans %}</h1>
  </div>
</header>
{% endblock %}

{% block footer %}
{% endblock %}

{% block content %}
<div class="row checkout">
  <div class="col-md-7">
    {% block forms %}{% endblock %}
  </div>
  <div class="col-md-5">
    <div class="checkout__review">
      <h3 class="checkout__review__tittle">Order review</h3>
      {% for line in current_user_cart.lines %}
      <div class="row">
        <div class="col-8">
          <p><strong>{{ line.variant.product }}</strong> x {{ line.quantity }}<br>
            <span class="checkout__review__variant">{{ line.variant }}</span>
          </p>
        </div>
        <div class="col-4">
          <p class="float-right">
            ${{ line.subtotal }}
          </p>
        </div>
      </div>
      {% endfor %}
      <div class="row checkout__review__section">
        <div class="col-8">
          <p>{% trans %}Subtotal{% endtrans %}</p>
        </div>
        <div class="col-4">
          <p class="float-right">
            ${{ current_user_cart.subtotal }}
          </p>
        </div>
      </div>
      <div class="row checkout__review__section">
        <div class="col-8">
          <p>{% trans %}Shipment{% endtrans %}</p>
        </div>
        <div class="col-4">
          <p class="float-right">
            {% if current_user_cart.shipping_method %}
            ${{current_user_cart.shipping_method_price }}
            {% else %}
            &mdash;
            {% endif %}
          </p>
        </div>
      </div>
      {% if voucher_form %}
      <div class="row checkout__review__section">
        <div class="col-12">
          <h3>{% trans %}Promo code{% endtrans %}</h3>
          {% if current_user_cart.voucher_code %}
          <form action="{{url_for('checkout.checkout_voucher_remove')}}" method="post" class="float-left input-btn">
            {{voucher_form.csrf_token}}
            <span>{{ current_user_cart.voucher.title }}</span>
            <button type="submit" class="btn btn-link btn-voucher-remove" aria-label=Remove">
              {% trans %}Remove{% endtrans %}
            </button>
          </form>
          <div class="float-right">
            -${{current_user_cart.discount_amount}}
          </div>
          {% else %}

          <form action="{{url_for('checkout.checkout_voucher')}}" method="post">
            {{voucher_form.csrf_token}}
            <div class="input-group mb-3">
              <input type="text" name="code" class="form-control" aria-label="voucher" aria-describedby="button-addon">
              <button class="btn secondary narrow" type="submit" id="button-addon">Use</button>
            </div>
          </form>
          {% endif %}
        </div>
      </div>
      {% endif %}
      <div class="row checkout__review__section">
        <div class="col-8">
          <h3>{% trans %}Total{% endtrans %}</h3>
        </div>
        <div class="col-4">
          <h3 class="float-right">${{ current_user_cart.total }}</h3>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock content %}
