{% extends "no_nav_base.html" %}

{% block title %}{% trans %}Your cart{% endtrans %}{% endblock %}

{% block breadcrumb %}
<ul class="breadcrumbs list-unstyled">
    <li><a href="{{ url_for('public.home') }}">{% trans %}Home{% endtrans %}</a></li>
    <li><a rel="nofollow" href="">{% trans %}Cart{% endtrans %}</a></li>
</ul>
{% endblock breadcrumb %}


{% block content %}
<div class="alert alert-success alert-dismissible fade remove-product-alert show d-none" role="alert">
    <span contenteditable="true">{% trans %}Product has been removed from cart{% endtrans %}</span>
    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
</div>
<div class="cart">
    {% if current_user_cart.lines %}
    <div class="table__header d-none d-md-block">
        <div class="row">
            <div class="col-md-7">
                <small>{% trans %}Product{% endtrans %}</small>
            </div>
            <div class="col-md-3">
                <small>{% trans %}Quantity{% endtrans %}</small>
            </div>
            <div class="col-md-2 text-right">
                <small>{% trans %}Price{% endtrans %}</small>
            </div>
        </div>
    </div>
    {% for line in current_user_cart.lines %}
    <div class="cart__line{% if loop.last %} last{% endif %} table__row">
        <div class="row">
            <div class="col-7 cart__line__product">
                <a class="link--clean" href="{{ line.product.get_absolute_url() }}">
                    <img class="lazyload lazypreload" data-src="{{ line.product.first_img }}" width="60" />
                    <p>{{ line.product }}<br>
                        <small>{{ line.variant }}</small>
                    </p>
                </a>
            </div>
            <div class="col-5">
                <div class="row">
                    <div class="cart__line__quantity col-md-7 col-12">
                        <form action="{{ url_for('checkout.update_cartline', id=line.id) }}" class="form-cart">
                            <div class="" tabindex="-1">
                                <input type="number" value="{{ line.quantity }}" min="1" max="50" id="id_quantity">
                            </div>
                        </form>
                        <span class="cart-item-delete">
                            <svg data-src="{{ url_for('static', filename='img/delete.svg') }}" height="20px"
                                width="20px"></svg>
                        </span>
                        <small class="cart__line__quantity-error text-danger"></small>
                    </div>
                    <div class="cart-item-price col-md-5 col-12" data-product-id="{{ line.variant.id }}">
                        <p class="text-right">
                            $ {{ line.subtotal }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
    <div class="cart__subtotal">
        {% include 'checkout/_subtotal_table.html' %}
    </div>
    <div class="row">
        <div class="col-md-12">
            <a href="{% if current_user_cart.is_shipping_required %}{{ url_for('checkout.checkout_shipping') }}{% else %}{{ url_for('checkout.checkout_note') }}{% endif %}"
                class="btn btn-primary float-right cart__submit">
                {% trans %}Checkout{% endtrans %}
            </a>
        </div>
    </div>
    {% else %}
    <div class="cart__empty">
        <img class="lazyload lazypreload" data-src="{{ url_for('static', filename='img/empty-cart-bg.png') }}"
            title="empty">
        <h2>{% trans %}There are no products in your shopping cart.{% endtrans %}</h2>
        <a href="{{ url_for('public.home') }}" class="btn btn-primary">{% trans %}Check out our sales{% endtrans %}</a>
    </div>
    {% endif %}
</div>
{% endblock content %}
