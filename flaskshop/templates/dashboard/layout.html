<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>The Dashboard</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://unpkg.com/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet"
          integrity="sha384-GLhlTQ8iRABdZLl6O3oVMWSktQOp6b7In1Zl3/Jr59b6EGGoI1aFkw7cmDA6j6gD" crossorigin="anonymous">
    <link href="https://unpkg.com/tom-select@2.2.2/dist/css/tom-select.css" rel="stylesheet">
    <link href="https://unpkg.com/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='dashboard/index.css') }}">
    {% block head_tail %}
    {% endblock %}
</head>

{% block page_body %}
    <body>
    <div>
        <nav class="main-header navbar navbar-expand border-bottom no-transition" id="header">
            <ul class="navbar-nav">
                <li>
                    <a class="nav-link" id="toggleSidebar" href="#" title="toggle"><i class="fa fa-bars"></i></a>
                </li>
                <li>
                    <a href="{{ url_for('dashboard.index') }}" class="nav-link">{% trans %}Dashboard{% endtrans %}</a>
                </li>
                <li>
                    <a href="{{ url_for('public.home') }}" class="nav-link" target="_blank">{% trans %}Front
                        Site{% endtrans %}</a>
                </li>
            </ul>
        </nav>

        <aside class="main-sidebar elevation-4">
            <a href="{{ url_for('dashboard.index') }}" class="brand-link">
                <img src="{{ url_for('static', filename='img/logo.svg') }}" class="brand-image" alt="logo">
            </a>

            <div class="sidebar">
                <div class="user-panel my-3">
                    <div class="image">
                        <img src="{{ current_user.avatar }}" class="rounded-circle" alt="User Image">
                    </div>
                    <div class="info">
                        <a href="#">{{ current_user.username }}</a>
                    </div>
                </div>
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column">
                        {%- for item in menus %}
                            <li class="has-treeview mt-2 {% if item.is_active() %}menu-open{% endif %}">
                                <a href="{{ item.get_url() }}"
                                   class="nav-link {%- if item.is_active() %} active{% endif %}">
                                    <i class="fa nav-icon {{ item.icon_cls }}"></i>
                                    <p>{{ item.title }}
                                        {%- if item.children %}<i class="right fa fa-angle-left"></i>{% endif %}
                                    </p>
                                </a>
                                {%- if item.children %}
                                    <ul class="nav nav-treeview">
                                        {%- for child in item.children -%}
                                            <li class="nav-item mt-2">
                                                <a href="{{ child.get_url() }}"
                                                   class="nav-link {%- if child.is_active() %} active{%- endif %}">
                                                    <i class="fa fa-circle-o nav-icon"></i>
                                                    <p>{{ child.title }}</p>
                                                </a>
                                            </li>
                                        {%- endfor %}
                                    </ul>
                                {% endif %}
                            </li>
                        {% endfor %}
                    </ul>
                </nav>
            </div>
        </aside>

        <div class="content-wrapper no-transition" id="content">
            {% block body %} {% endblock %}
        </div>
    </div>
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">Warning</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure to delete?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDelete">OK</button>
                </div>
            </div>
        </div>
    </div>
    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        <div id="deleteToast" class="toast align-items-center text-bg-primary border-0" role="alert"
             aria-live="assertive"
             aria-atomic="true" data-bs-config='{"delay":1000}'>
            <div class="d-flex">
                <div class="toast-body">
                    Poof! This item has been deleted!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                        aria-label="Close"></button>
            </div>
        </div>
        {% for message in get_flashed_messages(with_categories=True) %}
            <div class="toast align-items-center text-bg-{{ message[0] }} border-0 msg-toast" role="alert"
                 aria-live="assertive"
                 aria-atomic="true" data-bs-config='{"delay":2500}'>
                <div class="d-flex">
                    <div class="toast-body">
                        {{ message[1] }}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"
                            aria-label="Close"></button>
                </div>
            </div>
        {% endfor %}
    </div>
    <script src="https://unpkg.com/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js" defer></script>
    <script src="https://unpkg.com/tom-select@2.2.2/dist/js/tom-select.base.min.js" defer></script>
    <script src="{{ url_for('static', filename='dashboard/index.js') }}" defer></script>
    {% block tail_js %} {% endblock %}
    </body>
{% endblock %}

</html>
