{% extends 'dashboard/layout.html' %}
{% from 'bootstrap5/form.html' import render_form %}

{% block body %}
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h4 class="card-title">
                                {% if 'edit' in request.path %}
                                    {% trans %}Edit{% endtrans %}
                                {% else %}
                                    {% trans %}Create{% endtrans %}
                                {% endif %}
                                {% trans %}{{ title }}{% endtrans %}</h4>
                        </div>
                        <div class="card-body">
                            {{ render_form(form) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}