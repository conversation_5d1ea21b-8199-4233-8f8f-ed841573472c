{% extends 'dashboard/layout.html' %}

{% block body %}
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h4 class="card-title">{% trans %}Manage Plugins{% endtrans %}</h4>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body table-responsive p-0">
                        <table class="table table-hover">
                            <tr>
                                <th>{% trans %}Plugin{% endtrans %}</th>
                                <th>{% trans %}Info{% endtrans %}</th>
                                <th>{% trans %}Operation{% endtrans %}</th>
                            </tr>
                            {% for plugin in plugins %}
                            <tr>
                                <td>{{ plugin.name }}</td>
                                <td>
                                    <b>Version</b>: {{ plugin.info.get("version") }}<br>
                                    <b>Summary</b>: {{ plugin.info.get("summary")}}<br>
                                    <b>Author</b>: {{ plugin.info.get("author")}}<br>
                                    <b>Homepage</b>: <a href="{{ plugin.info.get("home_page")}}" target="_blank">{{ plugin.info.get("home_page")}}</a><br>
                                </td>
                                <td>
                                    {% if plugin.enabled %}
                                    <form method="POST" action="{{url_for('dashboard.plugin_disable', id=plugin.id)}}">
                                        <input type="hidden" name="csrf_token" value="{{csrf_token()}}" />
                                        <button type="submit" class="btn btn-warning">{% trans %}Disable{% endtrans %}</button>
                                    </form>
                                    {% else %}
                                    <form method="POST" action="{{url_for('dashboard.plugin_enable', id=plugin.id)}}">
                                        <input type="hidden" name="csrf_token" value="{{csrf_token()}}" />
                                        <button type="submit" class="btn btn-primary">{% trans %}Enable{% endtrans %}</button>
                                    </form>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </table>
                    </div>
                </div>
            </div>
        </div
    </div>
</section>
{% endblock %}
