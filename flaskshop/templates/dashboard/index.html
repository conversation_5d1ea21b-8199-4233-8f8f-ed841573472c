{% extends 'dashboard/layout.html' %}

{% block body %}
    <section>
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-8">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-box mb-3">
                            <span class="info-box-icon bg-success"><i
                                    class="fa fa-shopping-cart"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans %}Sales{% endtrans %}</span>
                                    <span class="info-box-text"><b>{{ orders_today }}</b> {% trans %}today
                                        and{% endtrans %} <b>{{ orders_total }}</b>
                                        {% trans %}total{% endtrans %}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-box mb-3">
                                <span class="info-box-icon bg-warning"><i class="fa fa-users"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">{% trans %}Members{% endtrans %}</span>
                                    <span class="info-box-text"><b>{{ users_today }}</b> {% trans %}new in{% endtrans %}
                                        <b>{{ users_total }}</b>
                                        {% trans %}accounts{% endtrans %}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-body p-0">
                            <ul class="navi-card">
                                <li class="item">
                                    <a href="{{ url_for('dashboard.orders', status=order_fulfill.kind) }}">
                                        <span>{{ order_fulfill.count }} {% trans %}Orders are ready to
                                            fulfill{% endtrans %}</span>
                                        <span class="right">></span>
                                    </a>
                                </li>
                                <li class="item">
                                    <a href="{{ url_for('dashboard.orders', status=order_unfulfill.kind) }}">
                                        <span>{{ order_unfulfill.count }} {% trans %}Payments to
                                            capture{% endtrans %}</span>
                                        <span class="right">></span>
                                    </a>
                                </li>
                                <li class="item">
                                    <a href="{{ url_for('dashboard.products', sale=True) }}">
                                        <span>{{ onsale_products_count }} {% trans %}products is on
                                            sale{% endtrans %}</span>
                                        <span class="right">></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title">{% trans %}Top Products{% endtrans %}</h6>
                        </div>
                        <div class="card-body p-0">
                            <ul class="products-list product-list-in-card px-2">
                                {% for product in top_products %}
                                    <li class="item">
                                        <img src="{{ product.first_img }}"
                                             alt="Product Image">
                                        <div class="product-info">
                                            <a href="{{ url_for('dashboard.product_detail', id=product.id) }}"
                                               class="product-title">{{ product.title }}
                                                <span class="badge text-bg-primary float-right">{{ product.price_human }}</span>
                                            </a>
                                            <span class="product-description">
                                                {{ product.description }}
                                            </span>
                                            <span class="product-description">
                                                {{ product.order_count }} {% trans %}Ordered{% endtrans %}
                                            </span>
                                        </div>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="card-title">{% trans %}Activity{% endtrans %}</h6>
                        </div>
                        <div class="card-body p-0">
                            <ul class="activity product-list-in-card px-2">
                                {% for a in activity %}
                                    <li class="item">
                                        <span class="activity-title">{% trans %}Order{% endtrans %} #{{ a.order_id }}
                                            {% trans %}was{% endtrans %} {{ order_events(a.type_).name }}</span>
                                        <span>{{ a.created_at_human }}</span>
                                    </li>
                                {% else %}
                                    <li class="item">
                                        <span class="activity-title">{% trans %}There is no recent
                                            activity{% endtrans %} </span>
                                    </li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
