{% extends 'dashboard/layout.html' %}
{% from 'bootstrap5/pagination.html' import render_pagination %}

{% block body %}
    <section>
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h4 class="card-title">{{ title }}</h4>
                            <div class="card-tools">
                                <div class="form-group">
                                    <div class="input-group input-group-sm">
                                        <a href="{{ url_for('dashboard.{}_manage'.format(identity)) }}"
                                           class="btn btn-success btn-sm">{% trans %}Create{% endtrans %}</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-body table-responsive p-3">
                            <table class="table table-hover">
                                <tr>
                                    {% for th in props.values() %}
                                        <th>{{ th }}</th>
                                    {% endfor %}
                                    <th>{% trans %}Operation{% endtrans %}</th>
                                </tr>
                                <tbody class="table-group-divider">
                                {% for item in items %}
                                    <tr>
                                        {% for prop in props.keys() %}
                                            <td>{{ item | attr(prop) }}</td>
                                        {% endfor %}
                                        <td>
                                            <a href="{{ url_for('dashboard.{}_manage'.format(identity), id=item.id) }}"
                                               class="btn btn-primary btn-sm">{% trans %}Edit{% endtrans %}</a>
                                            <a class="btn btn-danger btn-sm"
                                               data-delete-url="{{ identity }}/{{ item.id }}/delete"
                                               data-bs-toggle="modal" data-bs-target="#deleteModal"
                                            >{% trans %}Delete{% endtrans %}</a>
                                        </td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% if pagination.pages > 1 %}
                            <div class="card-footer">
                                {{ render_pagination(pagination, size='sm', align='right') }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
