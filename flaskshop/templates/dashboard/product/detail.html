{% extends 'dashboard/layout.html' %}
{% import 'dashboard/_macros.html' as macros %}

{% block body %}
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-outline">
                        <div class="card-header">
                            <h4 class="card-title">
                                <a href="{{ url_for('product.show', id=product.id) }}"
                                   target="_blank">{{ product.title }}</a>
                            </h4>
                            <div class="card-tools">
                                <a href="{{ url_for('dashboard.product_manage', id=product.id) }}"
                                   class="btn btn-primary btn-sm mx-2">{% trans %}
                                    Edit{% endtrans %}</a>
                                <a class="btn btn-danger btn-sm"
                                   data-delete-url="products/{{ product.id }}/delete"
                                   data-redirect-url="/dashboard/products"
                                   data-bs-toggle="modal" data-bs-target="#deleteModal"
                                >{% trans %}Delete{% endtrans %}</a>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div id="banner" class="carousel slide" data-bs-ride="true">
                                    <div class="carousel-inner">
                                        {% for img in product.images %}
                                            <div class="carousel-item {% if loop.first %}active{% endif %}">
                                                <img src="{{ img }}" class="d-block w-100" alt="...">
                                            </div>
                                        {% endfor %}
                                    </div>
                                    <button class="carousel-control-prev" type="button"
                                            data-bs-target="#banner" data-bs-slide="prev">
                                        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                        <span class="visually-hidden">Previous</span>
                                    </button>
                                    <button class="carousel-control-next" type="button"
                                            data-bs-target="#banner" data-bs-slide="next">
                                        <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                        <span class="visually-hidden">Next</span>
                                    </button>
                                </div>
                                <div class="product-attrs">
                                    <ul class="list-group mb-3">
                                        <li class="list-group-item">
                                            <b>{% trans %}On Sale{% endtrans %}</b> <span
                                                class="float-right">{{ macros.render_boolean(product.on_sale) }}</span>
                                        </li>
                                        <li class="list-group-item">
                                            <b>{% trans %}Price{% endtrans %}</b> <span
                                                class="float-right">${{ product.price }} /
                                            ${{ product.basic_price }}</span>
                                        </li>
                                        <li class="list-group-item">
                                            <b>{% trans %}Rating{% endtrans %}</b> <span
                                                class="float-right">{{ product.rating }}</span>
                                        </li>
                                        <li class="list-group-item">
                                            <b>{% trans %}Sold Count{% endtrans %}</b> <span
                                                class="float-right">{{ product.sold_count }}</span>
                                        </li>
                                        <li class="list-group-item">
                                            <b>{% trans %}Review Count{% endtrans %}</b> <span
                                                class="float-right">{{ product.review_count }}</span>
                                        </li>
                                        <li class="list-group-item">
                                            <b>{% trans %}Category{% endtrans %}</b> <span
                                                class="float-right">{{ product.category }}</span>
                                        </li>
                                        <li class="list-group-item">
                                            <b>{% trans %}ProductType{% endtrans %}</b> <span
                                                class="float-right">{{ product.product_type }}</span>
                                        </li>
                                        <li class="list-group-item">
                                            <b>{% trans %}Created{% endtrans %}</b> <span
                                                class="float-right">{{ product.created_at }}</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            <div class="row">
                                <p class="lead">{% trans %}Product Description{% endtrans %}:</p>
                                <p class="text-muted well well-sm no-shadow">
                                    {{ product.description }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="m-0">{% trans %}Variants{% endtrans %}</h5>
                            <div class="card-tools">
                                <div class="input-group input-group-sm">
                                    <a href="{{ url_for('dashboard.variant_manage', product_id=product.id) }}"
                                       class="btn btn-success btn-sm">{% trans %}Create{% endtrans %}</a>
                                </div>
                            </div>
                        </div>
                        <div class="card-body">
                            <table class="table table-hover">
                                <tr>
                                    <th>{% trans %}ID{% endtrans %}</th>
                                    <th>{% trans %}SKU{% endtrans %}</th>
                                    <th>{% trans %}Title{% endtrans %}</th>
                                    <th>{% trans %}Quantity{% endtrans %}</th>
                                    <th>{% trans %}PriceOverride{% endtrans %}</th>
                                    <th>{% trans %}Operation{% endtrans %}</th>
                                </tr>
                                {% for item in product.variant %}
                                    <tr>
                                        <td>{{ item.id }}</td>
                                        <td>{{ item.sku }}</td>
                                        <td>{{ item.title }}</td>
                                        <td>{{ item.quantity }}</td>
                                        <td>{{ item.price_override }}</td>
                                        <td>
                                            <a href="{{ url_for('dashboard.variant_manage', id=item.id) }}"
                                               class="btn btn-primary btn-sm">{% trans %}Edit{% endtrans %}</a>
                                            <a class="btn btn-danger btn-sm "
                                               data-delete-url="variants/{{ item.id }}/delete"
                                               data-bs-toggle="modal" data-bs-target="#deleteModal"
                                            >{% trans %}
                                                Delete{% endtrans %}</a>
                                        </td>
                                    </tr>
                                {% endfor %}
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

{% endblock %}

