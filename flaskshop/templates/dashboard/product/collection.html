{% extends 'dashboard/layout.html' %}
{% from 'bootstrap5/form.html' import render_field %}
{% import 'dashboard/_macros.html' as macros%}



{% block body %}
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">{% if 'edit' in request.path %}{% trans %}Edit{% endtrans %}{% else %}{% trans %}Create{% endtrans %}{% endif %}
                            {% trans %}Collection{% endtrans %}</h3>
                    </div>
                    <form method="POST" action="" enctype="multipart/form-data">
                        <div class="card-body">
                            {{ form.hidden_tag() }}
                            {{ render_field(form.title) }}
                            {{ render_field(form.products_ids) }}
                            {{ macros.render_img(form.background_img) }}
                            {{ render_field(form.bgimg_file) }}
                            {{ render_field(form.submit) }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

