{% extends 'dashboard/layout.html' %}
{% from 'bootstrap5/form.html' import render_field %}
{% import 'dashboard/_macros.html' as macros %}

{% block body %}
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h4 class="card-title">
                                {% if 'edit' in request.path %}{% trans %}Edit Product{% endtrans %}{% else %}{% trans %}Create Product Step 2{% endtrans %}{% endif %}
                            </h4>
                        </div>
                        <form method="POST" enctype=multipart/form-data>
                            <div class="card-body">
                                {{ form.hidden_tag() }}
                                <label>Type:</label> {{ product_type }}
                                {{ render_field(form.title) }}
                                {{ render_field(form.basic_price) }}
                                {{ render_field(form.on_sale) }}
                                {{ render_field(form.is_featured) }}
                                {{ render_field(form.rating) }}
                                {{ render_field(form.sold_count) }}
                                {{ render_field(form.review_count) }}
                                {{ render_field(form.category_id) }}
                                {{ render_field(form.description) }}
                                {{ macros.render_multi_img(form.images) }}
                                {{ render_field(form.new_images) }}
                                {{ macros.render_attribute_field(form.attributes, product_type.product_attributes) }}
                                {{ render_field(form.submit) }}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endblock %}

{% block tail_js %}
    <script>
        document.getElementById('field_list').addEventListener('click', function (event) {
            if (event.target.matches('#item_del')) {
                event.target.parentNode.remove();
            }
        });
    </script>
{% endblock %}
