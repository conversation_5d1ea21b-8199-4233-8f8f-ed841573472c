{% extends "base.html" %}


{% block title %}
Order {{ order }}
{% endblock %}

{% block breadcrumb %}
<ul class="breadcrumbs list-unstyled">
    <li>
        <a href="{{ url_for('public.home') }}">
            {% trans %}Home{% endtrans %}
        </a>
    </li>
    <li>
        <a href="{{ url_for('account.index') }}">
            {% trans %}Your account{% endtrans %}
        </a>
    </li>
    <li>
        <a href="{{ order.get_absolute_url() }}">
            {% trans %}Order{% endtrans %} {{ order }}
        </a>
    </li>
</ul>
{% endblock breadcrumb %}



{% block content %}

<div class="row order-details">
    <div class="col-lg-10 m-auto">
        <h2 class="order-details__header">
            {% trans %}Order{% endtrans %}: {{ order.token }}<br>
            {% trans %}Order Status{% endtrans %}: {{ order.status_human }}
        </h2>
        {% include "orders/_ordered_items_table.html" %}
        <div class="order-notes">
            {% if order.notes %}
            <h2 class="order-notes__header">{% trans %}Your note{% endtrans %}</h2>
            {% for note in order.notes %}
            <p>{{ note.content }}</p>
            {% endfor %}
            {% endif %}

        </div>
    </div>
</div>
{% endblock content %}
