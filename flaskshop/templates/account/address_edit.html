{% extends "no_nav_base.html" %}

{% block title %}
{% if address_id %}Edit address{% else %}New address{% endif %}
{% endblock %}

{% block breadcrumb %}
<ul class="breadcrumbs list-unstyled">
    <li>
        <a href="{{ url_for('public.home') }}">Home</a>
    </li>
    <li>
        <a href="{{ url_for('account.index') }}">
            {% trans %}Your profile{% endtrans %}
        </a>
    </li>

    <li>
        {% if address_id %}Edit address{% else %}New address{% endif %}
    </li>

</ul>
{% endblock breadcrumb %}

{% block content %}
<div class="row account__address-edit">
    <div class="col-sm-8 col-lg-6 m-auto">
        <form method="post" novalidate>
            <h2 class="text-center">
                {% if address_id %}{% trans %}Edit address{% endtrans %}{% else %}{% trans %}New address{% endtrans %}{%
                endif %}
            </h2>
            <div class="account__address-edit__form">
                {% include "account/partials/address_form.html" %}
            </div>
            <div class="text-end p-2">
                <button type="submit" class="btn btn-primary narrow">
                    {% trans %}Save changes{% endtrans %}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock content %}
