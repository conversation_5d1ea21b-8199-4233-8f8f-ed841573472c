{% extends "no_nav_base.html" %}
{% import '_macros.html' as macros %}
{% from 'bootstrap5/form.html' import render_field %}
{% block title %}Your profile{% endblock %}
{% block breadcrumb %}
<ul class="breadcrumbs list-unstyled">
  <li>
    <a href="{{ url_for('public.home') }}">
      {% trans %}Home{% endtrans %}
    </a>
  </li>
  <li>{% trans %}Your account{% endtrans %} (<b>{{ current_user }}</b>)</li>
</ul>
{% endblock %}

{% block content %}
<div class="account">
  <div class="account__nav container">
    <h2>{% trans %}My account{% endtrans %}</h2>
    <ul class="nav nav-tabs col-lg-10 m-auto" role="tablist">
      <li class="nav-item" role="presentation">
        <a class="nav-link active" data-bs-toggle="tab" data-bs-target="#orders" href="#" role="tab">
          <h3>{% trans %}Recent Orders{% endtrans %}</h3>
        </a>
      </li>

      <li class="nav-item" role="presentation">
        <a class="nav-link " data-bs-toggle="tab" data-bs-target="#password" href="#" role="tab">
          <h3>{% trans %}Change password{% endtrans %}</h3>
        </a>
      </li>

      <li class="nav-item" role="presentation">
        <a class="nav-link" data-bs-toggle="tab" data-bs-target="#addresses" href="#" role="tab">
          <h3>{% trans %}Addresses book{% endtrans %}</h3>
        </a>
      </li>
    </ul>
  </div>
  <div class="tab-content col-lg-10 m-auto">
    <div class="tab-pane active fade show" id="orders" role="tabpanel">
      <div id="accordion" role="tablist" aria-multiselectable="true">
        {% if orders %}
        <div class="table__header">
          <div class="row">
            <div class="col-md-2 col-3">
              <small>{% trans %}Order{% endtrans %}</small>
            </div>
            <div class="col-md-3 d-none d-md-block">
              <small>{% trans %}Date{% endtrans %}</small>
            </div>
            <div class="col-md-2 col-4 ">
              <small>{% trans %}Summary{% endtrans %}</small>
            </div>
            <div class="col-md-4 d-none d-md-block">
              <small>{% trans %}Status{% endtrans %}</small>
            </div>
            <div class="col-md-1 col-5"></div>
          </div>
        </div>
        {% for order in orders %}
        <div class="table__row">
          <div class="row">
            <div class="col-md-2 col-3">
              {{ order.identity }}
            </div>
            <div class="col-md-3 d-none d-md-block">
              {{ order.created_at }}
            </div>
            <div class="col-md-2 col-4">

              {{ order.total_human }}

            </div>
            <div class="col-md-4 d-none d-md-block">
              {{ order.status_human }}
            </div>
            <div class="col-md-1 col-5">
              <a href="{{ order.get_absolute_url() }}" class="float-right link--styled">
                {% trans %}Details{% endtrans %}
              </a>
            </div>
          </div>
        </div>
        {% endfor %}
        <div class="row mt-4">
          <div class="m-auto">
            {# pagnation#}
          </div>
        </div>
        {% else %}
        <h3 class="text-sm-center account__orders-empty">
          {% trans %}There are not any completed orders yet.{% endtrans %}
        </h3>
        {% endif %}
      </div>
    </div>
    <div class="tab-pane fade" id="password" role="tabpanel">
      <div class="row ">
        <div class="col-md-8 col-lg-6 m-auto account__password">
          <form action="{{ url_for('account.set_password') }}" method="post">
            {{ form.csrf_token }}
            {{ render_field(form.old_password) }}
            {{ render_field(form.password) }}
            {{ render_field(form.confirm) }}

            <input type="submit" class="btn btn-primary narrow" value="{% trans %}Change password{% endtrans %}">
          </form>
        </div>
      </div>
    </div>
    <div class="tab-pane fade" id="addresses" role="tabpanel">
      <div class="card-deck-wrapper account__addresses">
        <div class="row card-deck">
          {% for address in current_user.addresses %}
          <div class="col-md-6 myaddress">
            <div class="card card-block">
              {{macros.format_address(address)}}
              <ul class="icons">
                <li>
                  <a href="{{url_for('account.edit_address', id=address.id)}}">
                    {% trans %}Edit{% endtrans %}
                  </a>
                </li>
                <li>
                  <a class="delete-icon">
                    <svg data-src="{{ url_for('static', filename='img/delete.svg') }}" height="20px" width="20px"></svg>
                  </a>
                </li>
              </ul>
              <div class="address-delete none">
                <form method="post" novalidate action="{{url_for('account.delete_address', id=address.id)}}">
                  {{form.csrf_token}}
                  <button type="submit" class="btn danger narrow float-right">
                    {% trans %}Remove Address{% endtrans %}
                  </button>
                  <a class="btn btn-link cancel float-md-right">
                    {% trans %}Cancel{% endtrans %}
                  </a>
                </form>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>
        <p class="text-right">
          <a class="btn btn-primary narrow" href="{{url_for('account.edit_address')}}">
            {% trans %}New Address{% endtrans %}
          </a>
        </p>
      </div>
    </div>
  </div>
</div>
{% endblock %}
