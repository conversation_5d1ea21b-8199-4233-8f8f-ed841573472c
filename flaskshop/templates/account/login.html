{% extends "no_nav_base.html" %}

{% block title %}{% trans %}Log in{% endtrans %}{% endblock %}

{% block content %}
  <div class="col-lg-10 col-sm-12 m-auto">
    <div class="row login">
      <div class="col-md-6 login__register">
        <div class="login__register-link">
          <h3>{% trans %}Don't have an account yet?{% endtrans %}</h3>
          <img class="lazyload lazypreload"
               data-src="{{ url_for('static', filename='img/login-bg.png') }}"
               >
          <a rel="nofollow" href="{{ url_for('account.signup') }}" class="btn secondary narrow">
            {% trans %}Register{% endtrans %}
          </a>
        </div>
      </div>
      <div class="col-md-6 login__form">
        {% if not reset %}
          <h3>{% trans %}Log in{% endtrans %}</h3>
        {% else %}
            <h3>{% trans %}Reset Password{% endtrans %}</h3>
        {% endif %}
        {% include "account/partials/login_form.html" %}
      </div>
    </div>
  </div>
{% endblock %}
