{% import '_macros.html' as macros %}
<!DOCTYPE html>
<html class="no-js">

<head>
    <title>{% block title %}{% trans %}flask store{% endtrans %}{% endblock %}</title>
    {% block meta %}
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="generator" content="Saleor">
        <meta name="theme-color" content="#333333">
        <meta name="msapplication-TileColor" content="#2b5797">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="black">
        <meta name="og:type" content="website">
        <meta name="csrf-token" content="{{ csrf_token() }}">
    {% endblock meta %}
    <link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='build/storefront.css') }}">
    {% block stylesheet %}{% endblock stylesheet %}
</head>

<body>
{% block header %}
    <header class="navbar" role="navigation">
        <div class="navbar__login container-fluid">
            <div class=" d-none d-md-block container">
                <div class="row">
                    <div class="col-6">
                        <p class="text-left">{{ settings['project_title'].value }}
                            - {{ settings['project_subtitle'].value }}</p>
                    </div>
                    <div class="col-6">
                        <ul class="float-right">
                            {% if current_user.is_authenticated %}
                                {% if current_user.can_edit() %}

                                    <li>
                                        <a href="{{ url_for('dashboard.index') }}" target="_blank" rel="noopener">
                                            {% trans %}Dashboard{% endtrans %}
                                        </a>
                                    </li>
                                {% endif %}
                                {{ run_hook("flaskbb_tpl_user_nav_loggedin_before") }}
                                <li>
                                    <a href="{{ url_for('account.index') }}">
                                        {% trans %}Your Account{% endtrans %}</a>
                                </li>

                                <li>
                                    <a href="{{ url_for('account.logout') }}">
                                        {% trans %}Log Out{% endtrans %}</a>
                                </li>

                            {% else %}
                                <li>
                                    <a rel="nofollow" href="{{ url_for('account.signup') }}">
                                        {% trans %}Register{% endtrans %}
                                    </a>
                                </li>
                                <li>
                                    <a rel="nofollow" href="{{ url_for('account.login') }}">
                                        {% trans %}Log in{% endtrans %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="navbar__brand container-fluid d-block">
            <div class="container">
                <div class="row no-gutters">
                    <div class="col-8 col-md-4 navbar__logo">
                        <div class="menu-icon-mobile d-md-none navbar__brand__menu-toggle closed open">
                            <svg data-src="{{ url_for('static', filename='img/mobile-menu.svg') }}" width="28px"
                                 height="20px"></svg>
                        </div>
                        <a href="{{ url_for('public.home') }}">
                            <svg data-src="{{ url_for('static', filename='img/logo-document.svg') }}" width="176px"
                                 height="38px"></svg>
                        </a>
                    </div>
                    <div class="col-2 col-md-5 navbar__search static">
                        <div class="d-md-none mobile-search-icon">
                            <svg data-src="{{ url_for('static', filename='img/search.svg') }}" width="30"
                                 height="30"></svg>
                        </div>
                        <form class="form-inline search-form" action="/search">
                            <div class="mobile-close-search d-md-none">
                                <svg data-src="{{ url_for('static', filename='img/close.svg') }}" width="30"
                                     height="30"></svg>
                            </div>
                            <input class="form-control" type="text" name="q" value="{{ request.args.get('q', '') }}"
                                   placeholder="Search for product" required>
                            <button class="btn btn-primary" type="submit">
                                Search
                                <svg data-src="{{ url_for('static', filename='img/search.svg') }}" width="18"
                                     height="18"></svg>
                            </button>
                        </form>
                    </div>
                    <div class="col-2 col-md-3 position-relative">
                        <div class="navbar__brand__cart float-right">
                            <a rel="nofollow" class="cart__icon" href="{{ url_for('checkout.cart_index') }}">
                <span class="cart-label d-none d-md-inline-block">
                  {% trans %}Your Cart{% endtrans %}
                </span>
                                <div class="navbar__brand__cart__icon">
                                    <svg data-src="{{ url_for('static', filename='img/cart.svg') }}" width="24"
                                         height="24"></svg>
                                </div>
                                {% if current_user.is_authenticated and current_user_cart.quantity %}
                                    <span class="badge ">
                  {{ current_user_cart.quantity }}
                </span>
                                {% else %}
                                    <span class="badge empty">
                  0
                </span>
                                {% endif %}
                            </a>
                            <div class="cart-dropdown d-none">
                                {% include "public/cart_dropdown.html" %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {% block navigation %}
            <div class="navbar__menu container d-block">
                <nav class="navigation">
                    {{ macros.menu(top_menu) }}
                </nav>
            </div>
        {% endblock %}
    </header>
{% endblock %}
<div class="container maincontent">
    {% block topcontent %}{% endblock topcontent %}

    {% for message in get_flashed_messages(with_categories=True) %}
        <br>
        <div class="alert alert-{{ message[0] }} alert-dismissible fade show" role="alert">
            {{ message[1] }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    {% endfor %}

    {% block breadcrumb %}
    {% endblock breadcrumb %}
    {% block content %}
    {% endblock content %}
</div>
{% block footer %}
    <footer class="footer">
        <div class="footer__menus">
            <div class="container">
                <div class="row">
                    {{ macros.footer_menu(bottom_menu) }}
                    <div class="col-md-2 col-6">
                        <ul class="menu">
                            <li class="nav-item__dropdown menu__item">
                                <a>
                                    <strong>
                                        {% trans %}Account{% endtrans %}
                                    </strong>
                                </a>
                                <hr/>
                            <li>
                                <a rel="nofollow" href="{{ url_for('checkout.cart_index') }}">
                                    {% trans %}Your Cart{% endtrans %}
                                </a>
                            </li>
                            {% if current_user.is_authenticated %}
                                {% if current_user.can_edit() %}
                                    <li>
                                        <a href="{{ url_for('dashboard.index') }}" target="_blank" rel="noopener">
                                            {% trans %}Dashboard{% endtrans %}
                                        </a>
                                    </li>
                                {% endif %}
                                <li>
                                    <a href="{{ url_for('account.index') }}">
                                        {% trans %}Your account{% endtrans %}
                                    </a>
                                </li>

                                <li>
                                    <a href="{{ url_for('account.logout') }}">
                                        {% trans %}Log out{% endtrans %}</a>
                                </li>

                            {% else %}
                                <li>
                                    <a rel="nofollow" href="{{ url_for('account.signup') }}">
                                        {% trans %}Register{% endtrans %}
                                    </a>
                                </li>
                                <li>
                                    <a rel="nofollow" href="{{ url_for('account.login') }}">
                                        {% trans %}Log in{% endtrans %}
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                    <div class="offset-md-2 col-md-4 col-12">
                        <div class="footer__menus__social-container">
                            <div class="footer__menus__social">
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://www.facebook.com/mirumeelabs/">
                                    <svg data-src="{{ url_for('static', filename='img/facebook-logo.svg') }}"></svg>
                                </a>
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://github.com/mirumee/saleor">
                                    <svg data-src="{{ url_for('static', filename='img/github-logo.svg') }}"></svg>
                                </a>
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://twitter.com/getsaleor">
                                    <svg data-src="{{ url_for('static', filename='img/twitter-logo.svg') }}"></svg>
                                </a>
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://plus.google.com/+Mirumee">
                                    <svg data-src="{{ url_for('static', filename='img/google-logo.svg') }}"></svg>
                                </a>
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://www.meetup.com/Mirumee-Talks/">
                                    <svg data-src="{{ url_for('static', filename='img/meetup-logo.svg') }}"></svg>
                                </a>
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://linkedin.com/company/mirumee-software">
                                    <svg data-src="{{ url_for('static', filename='img/linkedin-logo.svg') }}"></svg>
                                </a>
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://dribbble.com/mirumee">
                                    <svg data-src="{{ url_for('static', filename='img/dribbble-logo.svg') }}"></svg>
                                </a>
                                <a target="_blank" rel="noopener" class="social-footer"
                                   href="https://www.instagram.com/explore/tags/mirumee/">
                                    <svg data-src="{{ url_for('static', filename='img/instagram-logo.svg') }}"></svg>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="footer__copy">
            <div class="container">
                <div class="row">
                    <div class="col-4">
                        <a href="{{ url_for('public.home') }}" class="footer__logo float-md-left">
                            <svg data-src="{{ url_for('static', filename='img/logo-document.svg') }}"></svg>
                        </a>
                    </div>
                    <div class="col-8 footer__copy-text">{{ settings['project_copyright'].value }}</div>
                </div>
            </div>
        </div>
    </footer>
{% endblock %}
{% block footer_scripts %}
    <script src="{{ url_for('static', filename='build/storefront.js') }}"></script>
    {% if config.GA_MEASUREMENT_ID %}
        <script async src="https://www.googletagmanager.com/gtag/js?id={{ config.GA_MEASUREMENT_ID }}"></script>
        <script>
            window.dataLayer = window.dataLayer || [];

            function gtag() {
                dataLayer.push(arguments);
            }

            gtag('js', new Date());
            gtag('config', '{{ config.GA_MEASUREMENT_ID }}');
        </script>
    {% endif %}
{% endblock footer_scripts %}
</body>

</html>
