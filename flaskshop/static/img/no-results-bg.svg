<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 width="360px" height="360px" viewBox="0 0 720 720" enable-background="new 0 0 720 720" xml:space="preserve">
<g>
	<defs>
		<circle id="SVGID_1_" cx="359.75" cy="361.057" r="350.25"/>
	</defs>
	<clipPath id="SVGID_2_">
		<use xlink:href="#SVGID_1_"  overflow="visible"/>
	</clipPath>
	<rect x="-15.285" y="-17.386" clip-path="url(#SVGID_2_)" fill="#C8E5EC" width="751.374" height="493.886"/>
	<g clip-path="url(#SVGID_2_)">
		<g opacity="0.9">
			<path fill="#FFFFFF" d="M180.169,250.249c0,0,0-71.167,71.167-71.167s71.167,71.167,71.167,71.167s35.585,0,35.585,35.583
				h-177.92V250.249z"/>
			<path fill="#FFFFFF" d="M109.002,321.416c0,0-35.583,0-35.583-35.584c0-35.583,35.583-35.583,35.583-35.583
				s35.585,0,35.585,35.583h142.334c0,0,0-35.583,35.583-35.583c0,0,35.585,0,35.585,35.583c0,0,0,35.584-35.585,35.584H109.002z"/>
			<path fill="#FFFFFF" d="M109.002,285.832c0,0,0-71.167,71.167-71.167c71.167,0,71.167,71.167,71.167,71.167H109.002z"/>
			<path fill="#FFFFFF" d="M180.169,321.416c0,0,0-71.167,71.167-71.167s71.167,71.167,71.167,71.167H180.169z"/>
		</g>
	</g>
	<g clip-path="url(#SVGID_2_)">
		<g opacity="0.9">
			<path fill="#FFFFFF" d="M590.455,389.175c0,0,0-30.077,30.076-30.077c30.077,0,30.077,30.077,30.077,30.077
				s15.039,0,15.039,15.037h-75.192V389.175z"/>
			<path fill="#FFFFFF" d="M560.378,419.251c0,0-15.037,0-15.037-15.039c0-15.037,15.037-15.037,15.037-15.037s15.04,0,15.04,15.037
				h60.153c0,0,0-15.037,15.037-15.037c0,0,15.039,0,15.039,15.037c0,0,0,15.039-15.039,15.039H560.378z"/>
			<path fill="#FFFFFF" d="M560.378,404.212c0,0,0-30.074,30.077-30.074c30.076,0,30.076,30.074,30.076,30.074H560.378z"/>
			<path fill="#FFFFFF" d="M590.455,419.251c0,0,0-30.076,30.076-30.076c30.077,0,30.077,30.076,30.077,30.076H590.455z"/>
		</g>
	</g>
	<rect x="-15.285" y="476.5" clip-path="url(#SVGID_2_)" fill="#57ACCD" width="751.374" height="257.487"/>
	<ellipse clip-path="url(#SVGID_2_)" fill="#3F89A0" cx="348.662" cy="593.902" rx="152.623" ry="23.48"/>
	<g clip-path="url(#SVGID_2_)">
		<polygon fill="#A6A8AC" points="193.428,596.516 216.908,573.035 381.271,549.555 451.713,526.074 475.193,549.555
			498.673,596.516 		"/>
		<path fill="#F0D4C0" d="M494.9,359.273c-4.986,10.367-14.818,15.985-21.96,12.551c-7.139-3.435-8.889-14.621-3.905-24.986
			c4.985-10.367,14.817-15.987,21.959-12.553C498.133,337.719,499.884,348.907,494.9,359.273z"/>
		<path fill="none" stroke="#237378" stroke-width="14.6391" stroke-miterlimit="10" d="M406.306,370.474
			c0,0,20.872,70.441,67.832,0"/>
		<path fill="none" stroke="#237378" stroke-width="10.4357" stroke-miterlimit="10" d="M324.952,445.694
			c0,0-11.121,63.919,5.401,105.662"/>
		<path fill="#237378" d="M301.194,465.267v11.302c0,0,0,22.614,58.556,22.614c58.559,0,58.559-22.614,58.559-22.614v-11.302
			H301.194z"/>
		<rect x="301.194" y="386.347" fill="#237378" width="117.114" height="83.699"/>
		<path fill="#455B5F" d="M428.983,303.905c-19.669-0.785,11.31-80.341-67.664-80.378l-20.68,1.378
			c-59.323,10.24-37.017,85.255-56.406,88.643c-19.394,3.387-33.801-39.667-31.019,2.067c2.779,41.733,42.189,44.347,42.189,44.347
			l64.334-4.287l64.337-4.287c0,0,29.775-7.223,26.996-48.957C448.288,260.7,448.655,304.688,428.983,303.905"/>
		<path fill="#F0D4C0" d="M311.179,352.775c0.492,7.408-3.89,13.731-9.793,14.126c-5.901,0.393-11.085-5.294-11.58-12.702
			c-0.492-7.407,3.893-13.73,9.791-14.124C305.5,339.684,310.685,345.369,311.179,352.775z"/>
		<path fill="#F0D4C0" d="M428.701,344.945c0.494,7.409-3.891,13.731-9.794,14.125c-5.898,0.394-11.083-5.293-11.577-12.701
			c-0.494-7.407,3.891-13.73,9.791-14.123C423.024,331.853,428.209,337.539,428.701,344.945z"/>
		<rect x="301.199" y="421.924" fill="#754D29" width="117.111" height="36.597"/>
		<path fill="none" stroke="#237378" stroke-width="14.6391" stroke-miterlimit="10" d="M308.175,390.973
			c0,0-58.482,25.03,3.595,56.164"/>
		<path fill="#F0D4C0" d="M362.247,399.523c52.884-3.523,55.886-36.087,55.666-45.293l-0.16-2.419l-0.974-14.606
			c0,0-3.245-36.462-60.858-32.623c-57.616,3.838-55.993,40.409-55.993,40.409l0.974,14.605l0.16,2.419
			C302.065,371.172,309.362,403.047,362.247,399.523"/>
		<path fill="#333333" d="M406.474,341.56c0.67,10.082-6.96,18.801-17.042,19.474c-10.081,0.67-18.803-6.959-19.473-17.042
			c-0.673-10.083,6.958-18.803,17.04-19.476C397.08,323.845,405.801,331.476,406.474,341.56"/>

			<line fill="none" stroke="#333333" stroke-width="4.0664" stroke-miterlimit="10" x1="358.976" y1="322.566" x2="412.135" y2="369.506"/>
		<path fill="#414142" d="M299.487,338.395l-0.046-0.707C299.441,337.688,299.452,337.937,299.487,338.395"/>
		<path fill="#414142" d="M416.291,329.901l0.048,0.708C416.313,330.151,416.291,329.901,416.291,329.901"/>
		<path fill="#57ACCD" d="M299.711,341.736c0,0,23.088-8.22,57.982-10.545c34.892-2.325,58.87,2.761,58.87,2.761
			s-0.594-33.371-60.643-29.37C295.872,308.582,299.711,341.736,299.711,341.736"/>
		<path fill="#333333" d="M354.234,346.746c0.207,3.094-2.135,5.771-5.23,5.977s-5.771-2.135-5.975-5.23
			c-0.207-3.096,2.132-5.771,5.228-5.977C351.353,341.309,354.028,343.65,354.234,346.746"/>

			<rect x="381.113" y="321.119" transform="matrix(0.9969 0.0792 -0.0792 0.9969 26.9061 -29.8359)" fill="#333333" width="17.081" height="6.443"/>
		<path fill="#333333" d="M356.277,269.875c0.665,0.191,1.435,0.395,2.232,0.598c1.131,0.285,2.209-0.613,2.132-1.773l-0.509-7.655
			c-0.046-0.687-0.5-1.288-1.154-1.502c-0.13-0.042-0.255-0.085-0.379-0.134c-1.353-0.477-1.84-1.135-1.921-2.386
			c-0.069-1.005,0.586-1.868,1.87-2.414c0.673-0.286,1.09-0.961,1.042-1.687l-0.069-1.032c-0.061-0.943,0.65-1.759,1.595-1.822
			l0.494-0.033c0.943-0.062,1.759,0.651,1.822,1.595l0.074,1.107c0.051,0.759,0.601,1.375,1.342,1.555
			c1.274,0.313,2.424,0.868,3.453,1.669c0.662,0.516,1.612,0.439,2.216-0.149l5.01-4.903c0.69-0.675,0.697-1.804-0.011-2.461
			c-3.786-3.502-8.912-5.039-15.376-4.608c-4.321,0.288-7.938,1.661-10.851,4.18c-2.912,2.521-4.204,5.814-3.926,9.945
			C345.752,263.849,349.414,267.817,356.277,269.875"/>
		<path fill="#333333" d="M368.69,261.818c-0.76-0.222-1.569-0.438-2.38-0.641c-1.126-0.285-2.201,0.613-2.125,1.773l0.522,7.832
			c0.046,0.712,0.532,1.315,1.215,1.526c0.003,0,0.006,0.002,0.011,0.002c1.732,0.577,2.54,1.341,2.619,2.53
			c0.087,1.321-0.649,2.284-2.209,2.875c-0.688,0.26-1.111,0.962-1.063,1.696l0.076,1.149c0.064,0.944-0.649,1.758-1.592,1.822
			l-0.494,0.032c-0.945,0.063-1.761-0.65-1.822-1.592l-0.068-1.012c-0.054-0.81-0.67-1.457-1.473-1.577
			c-2.792-0.42-5.3-1.669-7.188-3.44c-0.629-0.591-1.618-0.575-2.265-0.004l-5.467,4.814c-0.711,0.626-0.795,1.722-0.153,2.421
			c1.717,1.873,3.885,3.465,6.532,4.775c3.315,1.603,7.378,2.211,12.26,1.886c11.582-0.771,17.045-6.542,16.481-14.992
			C379.715,267.747,376.382,264.008,368.69,261.818"/>
		<path fill="#E4F1DF" d="M356.114,267.454c0.667,0.192,1.437,0.397,2.234,0.599c1.129,0.284,2.209-0.614,2.133-1.775l-0.512-7.653
			c-0.046-0.687-0.5-1.289-1.152-1.503c-0.13-0.041-0.255-0.085-0.382-0.132c-1.351-0.477-1.837-1.135-1.921-2.387
			c-0.066-1.005,0.586-1.868,1.873-2.414c0.67-0.285,1.091-0.96,1.042-1.684l-0.068-1.033c-0.064-0.943,0.649-1.759,1.595-1.822
			l0.492-0.033c0.945-0.062,1.76,0.651,1.824,1.595l0.073,1.105c0.049,0.762,0.602,1.375,1.341,1.558
			c1.276,0.313,2.425,0.868,3.454,1.667c0.663,0.518,1.613,0.439,2.217-0.149l5.007-4.902c0.69-0.676,0.7-1.805-0.008-2.461
			c-3.789-3.503-8.912-5.039-15.379-4.608c-4.321,0.288-7.936,1.66-10.848,4.181c-2.915,2.52-4.204,5.812-3.929,9.942
			C345.592,261.428,349.253,265.395,356.114,267.454"/>
		<path fill="#E4F1DF" d="M368.529,259.396c-0.759-0.222-1.571-0.438-2.379-0.641c-1.127-0.286-2.204,0.614-2.125,1.773l0.52,7.833
			c0.049,0.71,0.532,1.314,1.215,1.524c0.003,0.001,0.006,0.003,0.011,0.004c1.735,0.576,2.543,1.34,2.622,2.529
			c0.086,1.322-0.65,2.286-2.209,2.875c-0.688,0.26-1.111,0.962-1.063,1.696l0.076,1.149c0.063,0.944-0.649,1.761-1.595,1.823
			l-0.492,0.033c-0.945,0.063-1.761-0.652-1.821-1.595l-0.069-1.012c-0.053-0.81-0.672-1.457-1.472-1.576
			c-2.795-0.421-5.299-1.668-7.188-3.442c-0.629-0.591-1.62-0.572-2.265-0.002l-5.47,4.814c-0.711,0.625-0.792,1.722-0.15,2.42
			c1.717,1.874,3.883,3.465,6.532,4.774c3.313,1.603,7.379,2.211,12.26,1.887c11.583-0.772,17.045-6.542,16.482-14.991
			C379.552,265.326,376.222,261.587,368.529,259.396"/>
		<circle fill="#593C24" cx="360.849" cy="440.423" r="5.16"/>
		<circle fill="#593C24" cx="386.94" cy="440.423" r="5.159"/>
		<circle fill="#593C24" cx="405.202" cy="440.423" r="5.159"/>
		<circle fill="#593C24" cx="326.931" cy="440.423" r="5.161"/>
		<path fill="none" stroke="#F79643" stroke-width="9.7594" stroke-miterlimit="10" d="M369.516,458.521h-17.08
			c-5.368,0-9.758-4.393-9.758-9.759v-17.08c0-5.365,4.39-9.758,9.758-9.758h17.08c5.365,0,9.758,4.393,9.758,9.758v17.08
			C379.273,454.128,374.881,458.521,369.516,458.521z"/>
		<path fill="#333333" d="M398.033,373.908c0,0-13.523-19.474-30.025-10.227c-17.583-6.976-28.398,14.119-28.398,14.119
			c-27.741,1.85-39.697-33.867-40.123-39.406l2.792,41.894c1.982,29.758,39.356,27.943,39.356,27.943l30.184,12.663h0.002
			l28.237-16.556c0,0,21.053-2.079,19.07-31.835l-2.79-41.894C416.655,336.155,425.766,372.063,398.033,373.908 M369.226,381.943
			L369.226,381.943h-0.003l0,0c-20.242,2.028-22.313-4.626-22.313-4.626c11.529,1.946,21.48-7.526,21.503-7.547
			c0.025,0.018,11.146,8.084,22.313,4.627C390.726,374.397,389.554,381.266,369.226,381.943"/>
		<rect x="337.796" y="436.563" fill="#E7E7E7" width="21.957" height="7.317"/>
		<g>
				<rect x="462.17" y="359.313" transform="matrix(0.4965 -0.868 0.868 0.4965 -87.9687 596.1019)" fill="#216264" width="15.404" height="29.14"/>
				<rect x="470.719" y="354.357" transform="matrix(0.4969 -0.8678 0.8678 0.4969 -82.332 595.8392)" fill="#F79643" width="3.978" height="29.137"/>
		</g>
		<g>
			<path fill="#F0D4C0" d="M320.247,463.224c-4.782,6.313-12.736,8.336-17.766,4.524c-5.035-3.809-5.236-12.016-0.456-18.326
				c4.782-6.311,12.736-8.336,17.766-4.527C324.822,448.706,325.023,456.912,320.247,463.224z"/>

				<rect x="299.369" y="431.013" transform="matrix(0.8164 0.5774 -0.5774 0.8164 313.6682 -95.5206)" fill="#216264" width="15.403" height="29.139"/>

				<rect x="309.742" y="434.309" transform="matrix(0.8166 0.5772 -0.5772 0.8166 316.2598 -97.609)" fill="#F79643" width="3.981" height="29.141"/>
		</g>

			<ellipse transform="matrix(0.9978 -0.0665 0.0665 0.9978 -23.2004 25.1838)" fill="#E1B08E" cx="366.775" cy="361.168" rx="11.134" ry="3.913"/>
		<polygon fill="#593C24" points="312.358,529.619 318.682,567.553 299.716,573.874 299.716,580.197 331.33,580.197 337.65,529.619
					"/>
		<polygon fill="#754D29" points="312.639,523.097 300.898,546.577 347.859,546.577 336.119,523.097 		"/>
		<path fill="none" stroke="#333333" stroke-width="7.8268" stroke-miterlimit="10" d="M335.217,324.15
			c0,0,9.121-10.73,19.819-3.817"/>
		<g>
			<ellipse fill="#A6A8AC" cx="404.455" cy="341.775" rx="4.671" ry="7.827"/>
			<polygon fill="#A6A8AC" points="404.453,333.949 455.835,330.035 455.835,353.516 404.453,349.602 			"/>
			<ellipse fill="#929497" cx="455.833" cy="341.775" rx="4.671" ry="15.653"/>
			<polygon fill="#929497" points="455.835,326.122 530.566,318.295 530.566,365.256 455.835,357.429 			"/>
			<ellipse fill="#A6A8AC" cx="530.566" cy="341.775" rx="14.013" ry="23.48"/>
			<path fill="#414142" d="M539.909,341.775c0,10.807-4.181,19.567-9.343,19.567c-5.159,0-9.34-8.761-9.34-19.567
				s4.181-19.567,9.34-19.567C535.729,322.208,539.909,330.969,539.909,341.775z"/>
			<ellipse fill="#E7E7E7" cx="530.566" cy="335.905" rx="4.67" ry="9.784"/>
		</g>
		<path fill="none" stroke="#237378" stroke-width="10.4357" stroke-miterlimit="10" d="M400.043,477.007
			c0,0,65.491-39.137,41.748,30.869"/>
		<g>
			<polygon fill="#593C24" points="453.276,498.021 446.953,535.958 465.919,542.281 465.919,548.6 434.306,548.6 427.982,498.021
							"/>
			<polygon fill="#754D29" points="430.051,491.499 418.311,514.979 465.271,514.979 453.531,491.499 			"/>
		</g>
		<polygon fill="#929497" points="475.193,549.555 357.791,596.516 498.673,596.516 		"/>
		<polygon fill="#929497" points="240.389,596.516 216.908,573.035 334.311,596.516 		"/>
	</g>
</g>
</svg>
