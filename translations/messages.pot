# Translations template for PROJECT.
# Copyright (C) 2022 ORGANIZATION
# This file is distributed under the same license as the PROJECT project.
# <AUTHOR> <EMAIL>, 2022.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PROJECT VERSION\n"
"Report-Msgid-Bugs-To: EMAIL@ADDRESS\n"
"POT-Creation-Date: 2022-09-02 10:26+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Generated-By: Babel 2.10.3\n"

#: ../flaskshop/constant.py:32
msgid "Login user"
msgstr ""

#: ../flaskshop/constant.py:33
msgid "Editor"
msgstr ""

#: ../flaskshop/constant.py:34
msgid "Operator"
msgstr ""

#: ../flaskshop/constant.py:35
msgid "Super administrator"
msgstr ""

#: ../flaskshop/account/forms.py:17 ../flaskshop/dashboard/views/user.py:26
msgid "Username"
msgstr ""

#: ../flaskshop/account/forms.py:23
msgid "The username should contain only a-z, A-Z and 0-9."
msgstr ""

#: ../flaskshop/account/forms.py:28 ../flaskshop/account/forms.py:60
#: ../flaskshop/dashboard/views/user.py:27
msgid "Email"
msgstr ""

#: ../flaskshop/account/forms.py:31 ../flaskshop/account/forms.py:89
#: ../flaskshop/account/forms.py:122 ../flaskshop/dashboard/forms.py:85
msgid "Password"
msgstr ""

#: ../flaskshop/account/forms.py:34 ../flaskshop/account/forms.py:124
msgid "Verify password"
msgstr ""

#: ../flaskshop/account/forms.py:35 ../flaskshop/account/forms.py:127
msgid "Passwords must match"
msgstr ""

#: ../flaskshop/account/forms.py:50
msgid "Username already registered"
msgstr ""

#: ../flaskshop/account/forms.py:54
msgid "Email already registered"
msgstr ""

#: ../flaskshop/account/forms.py:76 ../flaskshop/account/forms.py:107
msgid "Unknown username"
msgstr ""

#: ../flaskshop/account/forms.py:79 ../flaskshop/account/forms.py:115
msgid "User not activated"
msgstr ""

#: ../flaskshop/account/forms.py:82
msgid "Invalid"
msgstr ""

#: ../flaskshop/account/forms.py:88
msgid "Username Or Email"
msgstr ""

#: ../flaskshop/account/forms.py:111 ../flaskshop/account/forms.py:143
msgid "Invalid password"
msgstr ""

#: ../flaskshop/account/forms.py:121
msgid "Old Password"
msgstr ""

#: ../flaskshop/account/forms.py:151 ../flaskshop/dashboard/forms.py:94
msgid "Province"
msgstr ""

#: ../flaskshop/account/forms.py:152
msgid "City"
msgstr ""

#: ../flaskshop/account/forms.py:153 ../flaskshop/dashboard/forms.py:96
msgid "District"
msgstr ""

#: ../flaskshop/account/forms.py:154 ../flaskshop/dashboard/forms.py:97
msgid "Address"
msgstr ""

#: ../flaskshop/account/forms.py:155
msgid "Contact name"
msgstr ""

#: ../flaskshop/account/forms.py:157 ../flaskshop/dashboard/forms.py:99
#: ../flaskshop/templates/dashboard/user/detail.html:57
msgid "Contact Phone"
msgstr ""

#: ../flaskshop/account/views.py:31
msgid "You are log in."
msgstr ""

#: ../flaskshop/account/views.py:46
msgid "Check your e-mail."
msgstr ""

#: ../flaskshop/account/views.py:50
#: ../flaskshop/templates/account/login.html:23
#: ../flaskshop/templates/account/partials/login_form.html:16
msgid "Reset Password"
msgstr ""

#: ../flaskshop/account/views.py:52
#, python-format
msgid ""
"We cannot simply send you your old password.\n"
" \n"
"        A unique password has been generated for you. Change the password"
" after logging in.\n"
"\n"
"        New Password is: %s"
msgstr ""

#: ../flaskshop/account/views.py:68
msgid "You are logged out."
msgstr ""

#: ../flaskshop/account/views.py:83
msgid "You are signed up."
msgstr ""

#: ../flaskshop/account/views.py:94
msgid "You have changed password."
msgstr ""

#: ../flaskshop/account/views.py:125
msgid "Success edit address."
msgstr ""

#: ../flaskshop/account/views.py:128
msgid "Success add address."
msgstr ""

#: ../flaskshop/api/checkout.py:5
msgid "Checkout related operations"
msgstr ""

#: ../flaskshop/api/checkout.py:10
msgid "The checkout cartline id"
msgstr ""

#: ../flaskshop/api/checkout.py:11
msgid "The cart item num"
msgstr ""

#: ../flaskshop/api/checkout.py:13
msgid "The cart item title"
msgstr ""

#: ../flaskshop/api/checkout.py:16
msgid "The cart item variant"
msgstr ""

#: ../flaskshop/api/checkout.py:19
msgid "The cart item product"
msgstr ""

#: ../flaskshop/api/checkout.py:22
msgid "The cart item price"
msgstr ""

#: ../flaskshop/api/checkout.py:25
msgid "The cart item image"
msgstr ""

#: ../flaskshop/api/product.py:9
msgid "Products related operations"
msgstr ""

#: ../flaskshop/api/product.py:11
msgid "The variant"
msgstr ""

#: ../flaskshop/api/product.py:12
msgid "The quantity"
msgstr ""

#: ../flaskshop/api/product.py:17 ../flaskshop/api/product.py:39
#: ../flaskshop/api/product.py:63
msgid "The product identifier"
msgstr ""

#: ../flaskshop/api/product.py:18 ../flaskshop/api/product.py:40
msgid "The product name"
msgstr ""

#: ../flaskshop/api/product.py:19 ../flaskshop/api/product.py:41
msgid "The product description"
msgstr ""

#: ../flaskshop/api/product.py:20 ../flaskshop/api/product.py:42
msgid "The product price"
msgstr ""

#: ../flaskshop/api/product.py:21
msgid "The product first img"
msgstr ""

#: ../flaskshop/api/product.py:27
msgid "The variant identifier"
msgstr ""

#: ../flaskshop/api/product.py:28
msgid "The variant sku"
msgstr ""

#: ../flaskshop/api/product.py:29
msgid "The variant name"
msgstr ""

#: ../flaskshop/api/product.py:30
msgid "The variant price"
msgstr ""

#: ../flaskshop/api/product.py:32
msgid "The variant stock"
msgstr ""

#: ../flaskshop/api/product.py:43
msgid "The product images"
msgstr ""

#: ../flaskshop/api/product.py:45
msgid "The product variant"
msgstr ""

#: ../flaskshop/api/product.py:64
msgid "Product not found"
msgstr ""

#: ../flaskshop/checkout/forms.py:6
msgid "ADD A NOTE TO YOUR ORDER"
msgstr ""

#: ../flaskshop/checkout/forms.py:10 ../flaskshop/dashboard/forms.py:174
msgid "Code"
msgstr ""

#: ../flaskshop/checkout/views.py:118
msgid "Your code is not correct"
msgstr ""

#: ../flaskshop/dashboard/forms.py:48 ../flaskshop/dashboard/forms.py:57
#: ../flaskshop/dashboard/forms.py:69 ../flaskshop/dashboard/forms.py:104
#: ../flaskshop/dashboard/forms.py:111 ../flaskshop/dashboard/forms.py:119
#: ../flaskshop/dashboard/forms.py:127 ../flaskshop/dashboard/forms.py:136
#: ../flaskshop/dashboard/forms.py:159 ../flaskshop/dashboard/forms.py:166
#: ../flaskshop/dashboard/forms.py:172 ../flaskshop/dashboard/forms.py:193
#: ../flaskshop/dashboard/views/discount.py:16
#: ../flaskshop/dashboard/views/discount.py:70
#: ../flaskshop/dashboard/views/product.py:30
#: ../flaskshop/dashboard/views/product.py:67
#: ../flaskshop/dashboard/views/product.py:108
#: ../flaskshop/dashboard/views/product.py:152
#: ../flaskshop/dashboard/views/product.py:212
#: ../flaskshop/dashboard/views/site.py:24
#: ../flaskshop/dashboard/views/site.py:58
#: ../flaskshop/dashboard/views/site.py:106
#: ../flaskshop/dashboard/views/site.py:144
#: ../flaskshop/templates/dashboard/product/detail.html:96
msgid "Title"
msgstr ""

#: ../flaskshop/dashboard/forms.py:49 ../flaskshop/dashboard/forms.py:58
#: ../flaskshop/dashboard/views/site.py:59
#: ../flaskshop/dashboard/views/site.py:107
#: ../flaskshop/templates/account/details.html:47
#: ../flaskshop/templates/dashboard/index.html:105
#: ../flaskshop/templates/orders/details.html:22
#: ../flaskshop/templates/orders/details.html:35
#: ../flaskshop/templates/public/style_guide.html:714
msgid "Order"
msgstr ""

#: ../flaskshop/dashboard/forms.py:50
msgid "End Point"
msgstr ""

#: ../flaskshop/dashboard/forms.py:51
msgid "Icon"
msgstr ""

#: ../flaskshop/dashboard/forms.py:52 ../flaskshop/dashboard/forms.py:60
#: ../flaskshop/dashboard/forms.py:120
#: ../flaskshop/dashboard/views/product.py:109
msgid "Parent"
msgstr ""

#: ../flaskshop/dashboard/forms.py:53 ../flaskshop/dashboard/forms.py:65
#: ../flaskshop/dashboard/forms.py:73 ../flaskshop/dashboard/forms.py:79
#: ../flaskshop/dashboard/forms.py:90 ../flaskshop/dashboard/forms.py:100
#: ../flaskshop/dashboard/forms.py:107 ../flaskshop/dashboard/forms.py:115
#: ../flaskshop/dashboard/forms.py:123 ../flaskshop/dashboard/forms.py:132
#: ../flaskshop/dashboard/forms.py:147 ../flaskshop/dashboard/forms.py:152
#: ../flaskshop/dashboard/forms.py:162 ../flaskshop/dashboard/forms.py:168
#: ../flaskshop/dashboard/forms.py:189 ../flaskshop/dashboard/forms.py:198
msgid "Submit"
msgstr ""

#: ../flaskshop/dashboard/forms.py:59 ../flaskshop/dashboard/views/site.py:146
msgid "Url"
msgstr ""

#: ../flaskshop/dashboard/forms.py:61 ../flaskshop/dashboard/views/site.py:60
msgid "Position"
msgstr ""

#: ../flaskshop/dashboard/forms.py:61
msgid "none"
msgstr ""

#: ../flaskshop/dashboard/forms.py:61
msgid "top"
msgstr ""

#: ../flaskshop/dashboard/forms.py:61
msgid "bottom"
msgstr ""

#: ../flaskshop/dashboard/forms.py:62 ../flaskshop/dashboard/forms.py:143
#: ../flaskshop/dashboard/forms.py:184 ../flaskshop/dashboard/forms.py:196
#: ../flaskshop/dashboard/views/product.py:216
#: ../flaskshop/templates/dashboard/product/category.html:15
#: ../flaskshop/templates/dashboard/product/detail.html:56
#: ../flaskshop/templates/dashboard/product/list.html:26
msgid "Category"
msgstr ""

#: ../flaskshop/dashboard/forms.py:63
#: ../flaskshop/templates/dashboard/product/collection.html:15
msgid "Collection"
msgstr ""

#: ../flaskshop/dashboard/forms.py:64
#: ../flaskshop/templates/dashboard/site/site_page.html:14
msgid "Page"
msgstr ""

#: ../flaskshop/dashboard/forms.py:70 ../flaskshop/dashboard/views/site.py:145
msgid "Slug"
msgstr ""

#: ../flaskshop/dashboard/forms.py:71
msgid "Content"
msgstr ""

#: ../flaskshop/dashboard/forms.py:72
msgid "Is Visible"
msgstr ""

#: ../flaskshop/dashboard/forms.py:77
msgid "Header"
msgstr ""

#: ../flaskshop/dashboard/forms.py:78 ../flaskshop/dashboard/forms.py:144
#: ../flaskshop/templates/products/details.html:135
msgid "Description"
msgstr ""

#: ../flaskshop/dashboard/forms.py:83
msgid "User Name"
msgstr ""

#: ../flaskshop/dashboard/forms.py:84
msgid "E-mail"
msgstr ""

#: ../flaskshop/dashboard/forms.py:86
msgid "Is Activ"
msgstr ""

#: ../flaskshop/dashboard/forms.py:87
msgid "Role"
msgstr ""

#: ../flaskshop/dashboard/forms.py:88
msgid "Created at"
msgstr ""

#: ../flaskshop/dashboard/forms.py:89
msgid "Updated at"
msgstr ""

#: ../flaskshop/dashboard/forms.py:95
msgid "Sity"
msgstr ""

#: ../flaskshop/dashboard/forms.py:98
#: ../flaskshop/templates/dashboard/user/detail.html:56
msgid "Contact Name"
msgstr ""

#: ../flaskshop/dashboard/forms.py:105
#: ../flaskshop/dashboard/views/product.py:31
msgid "Value"
msgstr ""

#: ../flaskshop/dashboard/forms.py:106
#: ../flaskshop/templates/dashboard/site/index.html:27
msgid "Product Types"
msgstr ""

#: ../flaskshop/dashboard/forms.py:112
msgid "Products"
msgstr ""

#: ../flaskshop/dashboard/forms.py:113 ../flaskshop/dashboard/forms.py:121
msgid "Current Image"
msgstr ""

#: ../flaskshop/dashboard/forms.py:114 ../flaskshop/dashboard/forms.py:122
msgid "Upload"
msgstr ""

#: ../flaskshop/dashboard/forms.py:128
msgid "Has variant"
msgstr ""

#: ../flaskshop/dashboard/forms.py:129
msgid "Is shipping required"
msgstr ""

#: ../flaskshop/dashboard/forms.py:130
msgid "Product atributes"
msgstr ""

#: ../flaskshop/dashboard/forms.py:131
msgid "Variant Attributes"
msgstr ""

#: ../flaskshop/dashboard/forms.py:137
msgid "Basic Price"
msgstr ""

#: ../flaskshop/dashboard/forms.py:138
#: ../flaskshop/dashboard/views/product.py:213
#: ../flaskshop/templates/dashboard/product/detail.html:39
#: ../flaskshop/templates/dashboard/product/list.html:19
msgid "On Sale"
msgstr ""

#: ../flaskshop/dashboard/forms.py:139
msgid "Is Featured"
msgstr ""

#: ../flaskshop/dashboard/forms.py:140
#: ../flaskshop/templates/dashboard/product/detail.html:47
msgid "Rating"
msgstr ""

#: ../flaskshop/dashboard/forms.py:141
#: ../flaskshop/dashboard/views/product.py:214
#: ../flaskshop/templates/dashboard/product/detail.html:50
msgid "Sold Count"
msgstr ""

#: ../flaskshop/dashboard/forms.py:142
#: ../flaskshop/templates/dashboard/product/detail.html:53
msgid "Review Count"
msgstr ""

#: ../flaskshop/dashboard/forms.py:145
msgid "Images"
msgstr ""

#: ../flaskshop/dashboard/forms.py:146
msgid "Atributes"
msgstr ""

#: ../flaskshop/dashboard/forms.py:151
msgid "Choose A Product Type"
msgstr ""

#: ../flaskshop/dashboard/forms.py:157
#: ../flaskshop/templates/dashboard/order/detail.html:51
#: ../flaskshop/templates/dashboard/product/detail.html:95
msgid "SKU"
msgstr ""

#: ../flaskshop/dashboard/forms.py:160
msgid "Price override"
msgstr ""

#: ../flaskshop/dashboard/forms.py:161
#: ../flaskshop/templates/checkout/cart.html:26
#: ../flaskshop/templates/dashboard/product/detail.html:97
#: ../flaskshop/templates/orders/_ordered_items_table.html:12
#: ../flaskshop/templates/public/style_guide.html:573
msgid "Quantity"
msgstr ""

#: ../flaskshop/dashboard/forms.py:167
#: ../flaskshop/dashboard/views/product.py:215
#: ../flaskshop/dashboard/views/site.py:25
#: ../flaskshop/templates/checkout/cart.html:29
#: ../flaskshop/templates/dashboard/product/detail.html:43
#: ../flaskshop/templates/orders/_ordered_items_table.html:17
#: ../flaskshop/templates/products/_filters.html:49
msgid "Price"
msgstr ""

#: ../flaskshop/dashboard/forms.py:173
#: ../flaskshop/dashboard/views/discount.py:17
#: ../flaskshop/templates/dashboard/product/product_create_step2.html:17
msgid "Type"
msgstr ""

#: ../flaskshop/dashboard/forms.py:175
msgid "Usage limit"
msgstr ""

#: ../flaskshop/dashboard/forms.py:176
msgid "how many times can be used"
msgstr ""

#: ../flaskshop/dashboard/forms.py:178
#: ../flaskshop/dashboard/views/discount.py:19
msgid "Used"
msgstr ""

#: ../flaskshop/dashboard/forms.py:179
msgid "Validity Period"
msgstr ""

#: ../flaskshop/dashboard/forms.py:180 ../flaskshop/dashboard/forms.py:194
msgid "Discount value type"
msgstr ""

#: ../flaskshop/dashboard/forms.py:181 ../flaskshop/dashboard/forms.py:195
msgid "Discount value"
msgstr ""

#: ../flaskshop/dashboard/forms.py:182
msgid "Limit"
msgstr ""

#: ../flaskshop/dashboard/forms.py:184
msgid "when type is category, need to select"
msgstr ""

#: ../flaskshop/dashboard/forms.py:187 ../flaskshop/dashboard/forms.py:197
#: ../flaskshop/templates/checkout/cart.html:23
#: ../flaskshop/templates/dashboard/order/detail.html:50
#: ../flaskshop/templates/orders/_ordered_items_table.html:7
msgid "Product"
msgstr ""

#: ../flaskshop/dashboard/forms.py:187
msgid "when type is product, need to select"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:15
#: ../flaskshop/dashboard/views/discount.py:69
#: ../flaskshop/dashboard/views/order.py:27
#: ../flaskshop/dashboard/views/product.py:29
#: ../flaskshop/dashboard/views/product.py:67
#: ../flaskshop/dashboard/views/product.py:107
#: ../flaskshop/dashboard/views/product.py:151
#: ../flaskshop/dashboard/views/product.py:211
#: ../flaskshop/dashboard/views/site.py:23
#: ../flaskshop/dashboard/views/site.py:57
#: ../flaskshop/dashboard/views/site.py:105
#: ../flaskshop/dashboard/views/site.py:143
#: ../flaskshop/dashboard/views/user.py:25
#: ../flaskshop/templates/dashboard/product/detail.html:94
#: ../flaskshop/templates/dashboard/user/detail.html:55
#: ../flaskshop/templates/dashboard/user/detail.html:80
msgid "ID"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:18
msgid "Usage Limit"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:20
#: ../flaskshop/dashboard/views/discount.py:71
msgid "Discount Type"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:21
#: ../flaskshop/dashboard/views/discount.py:72
msgid "Discount Value"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:24
#: ../flaskshop/templates/dashboard/discount/voucher.html:15
msgid "Voucher"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:28
msgid "vouchers"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:75
#: ../flaskshop/templates/dashboard/discount/sale.html:14
#: ../flaskshop/templates/products/_items.html:14
#: ../flaskshop/templates/public/style_guide.html:841
msgid "Sale"
msgstr ""

#: ../flaskshop/dashboard/views/discount.py:79
msgid "sales"
msgstr ""

#: ../flaskshop/dashboard/views/order.py:28
msgid "Identity"
msgstr ""

#: ../flaskshop/dashboard/views/order.py:29
#: ../flaskshop/templates/account/details.html:56
#: ../flaskshop/templates/dashboard/order/detail.html:22
#: ../flaskshop/templates/dashboard/order/list.html:16
#: ../flaskshop/templates/dashboard/user/detail.html:81
#: ../flaskshop/templates/public/style_guide.html:723
msgid "Status"
msgstr ""

#: ../flaskshop/dashboard/views/order.py:30
#: ../flaskshop/templates/checkout/_subtotal_table.html:4
#: ../flaskshop/templates/checkout/details.html:101
#: ../flaskshop/templates/dashboard/order/detail.html:106
#: ../flaskshop/templates/dashboard/user/detail.html:82
#: ../flaskshop/templates/orders/_ordered_items_table.html:101
msgid "Total"
msgstr ""

#: ../flaskshop/dashboard/views/order.py:31
msgid "User"
msgstr ""

#: ../flaskshop/dashboard/views/order.py:32
#: ../flaskshop/dashboard/views/product.py:67
#: ../flaskshop/dashboard/views/product.py:110
#: ../flaskshop/dashboard/views/product.py:155
#: ../flaskshop/dashboard/views/site.py:26
#: ../flaskshop/templates/dashboard/order/detail.html:14
#: ../flaskshop/templates/dashboard/order/list.html:28
#: ../flaskshop/templates/dashboard/user/detail.html:83
msgid "Created At"
msgstr ""

#: ../flaskshop/dashboard/views/product.py:32
#: ../flaskshop/templates/dashboard/product/detail.html:59
msgid "ProductType"
msgstr ""

#: ../flaskshop/dashboard/views/product.py:35
msgid "Product Attribute"
msgstr ""

#: ../flaskshop/dashboard/views/product.py:69
msgid "Product Collection"
msgstr ""

#: ../flaskshop/dashboard/views/product.py:113
msgid "Product Category"
msgstr ""

#: ../flaskshop/dashboard/views/product.py:153
msgid "Has Variants"
msgstr ""

#: ../flaskshop/dashboard/views/product.py:154
msgid "Is Shipping Required"
msgstr ""

#: ../flaskshop/dashboard/views/product.py:158
#: ../flaskshop/templates/dashboard/product/product_type.html:15
msgid "Product Type"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:29
#: ../flaskshop/templates/dashboard/order/detail.html:35
#: ../flaskshop/templates/dashboard/site/shipping_method.html:15
msgid "Shipping Method"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:61
#: ../flaskshop/dashboard/views/site.py:110
msgid "Parent Id"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:64
msgid "Site Menus"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:108
msgid "Endpoint"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:109
msgid "Icon class"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:113
msgid "Dashboard Menus"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:147
msgid "Is Visiable"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:150
msgid "Site Pages"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:183
msgid "The plugin is enabled, Please restart flask-shop now!"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:191
msgid "The plugin is disabled, Please restart flask-shop now!"
msgstr ""

#: ../flaskshop/dashboard/views/site.py:219
msgid "Settings saved."
msgstr ""

#: ../flaskshop/dashboard/views/user.py:28
msgid "Is Active"
msgstr ""

#: ../flaskshop/dashboard/views/user.py:31
msgid "User List"
msgstr ""

#: ../flaskshop/order/views.py:34
msgid "This is not your order!"
msgstr ""

#: ../flaskshop/order/views.py:41
msgid "This Order Can Not Pay"
msgstr ""

#: ../flaskshop/templates/base.html:16
msgid "flask store"
msgstr ""

#: ../flaskshop/templates/base.html:48 ../flaskshop/templates/base.html:186
#: ../flaskshop/templates/dashboard/layout.html:22
msgid "Dashboard"
msgstr ""

#: ../flaskshop/templates/base.html:55
msgid "Your Account"
msgstr ""

#: ../flaskshop/templates/base.html:60
msgid "Log Out"
msgstr ""

#: ../flaskshop/templates/account/login.html:15
#: ../flaskshop/templates/base.html:66 ../flaskshop/templates/base.html:204
msgid "Register"
msgstr ""

#: ../flaskshop/templates/account/login.html:3
#: ../flaskshop/templates/account/login.html:21
#: ../flaskshop/templates/account/partials/login_form.html:14
#: ../flaskshop/templates/account/signup.html:13
#: ../flaskshop/templates/base.html:71 ../flaskshop/templates/base.html:209
msgid "Log in"
msgstr ""

#: ../flaskshop/templates/base.html:114 ../flaskshop/templates/base.html:179
msgid "Your Cart"
msgstr ""

#: ../flaskshop/templates/base.html:173
#: ../flaskshop/templates/dashboard/order/detail.html:25
msgid "Account"
msgstr ""

#: ../flaskshop/templates/account/details.html:12
#: ../flaskshop/templates/base.html:192
#: ../flaskshop/templates/orders/details.html:17
msgid "Your account"
msgstr ""

#: ../flaskshop/templates/base.html:198
msgid "Log out"
msgstr ""

#: ../flaskshop/templates/account/address_edit.html:14
#: ../flaskshop/templates/public/style_guide.html:612
msgid "Your profile"
msgstr ""

#: ../flaskshop/templates/account/address_edit.html:30
#: ../flaskshop/templates/public/style_guide.html:617
msgid "Edit address"
msgstr ""

#: ../flaskshop/templates/account/address_edit.html:30
msgid "New address"
msgstr ""

#: ../flaskshop/templates/account/address_edit.html:37
msgid "Save changes"
msgstr ""

#: ../flaskshop/templates/account/details.html:9
#: ../flaskshop/templates/checkout/cart.html:7
#: ../flaskshop/templates/orders/details.html:12
#: ../flaskshop/templates/public/page.html:13
#: ../plugin_example/conversations/templates/message_layout.html:7
msgid "Home"
msgstr ""

#: ../flaskshop/templates/account/details.html:19
msgid "My account"
msgstr ""

#: ../flaskshop/templates/account/details.html:23
#: ../flaskshop/templates/public/style_guide.html:668
msgid "Recent Orders"
msgstr ""

#: ../flaskshop/templates/account/details.html:29
#: ../flaskshop/templates/account/details.html:107
#: ../flaskshop/templates/public/style_guide.html:673
msgid "Change password"
msgstr ""

#: ../flaskshop/templates/account/details.html:35
#: ../flaskshop/templates/public/style_guide.html:678
msgid "Addresses book"
msgstr ""

#: ../flaskshop/templates/account/details.html:50
#: ../flaskshop/templates/public/style_guide.html:717
msgid "Date"
msgstr ""

#: ../flaskshop/templates/account/details.html:53
#: ../flaskshop/templates/public/style_guide.html:720
msgid "Summary"
msgstr ""

#: ../flaskshop/templates/account/details.html:80
#: ../flaskshop/templates/public/style_guide.html:744
#: ../flaskshop/templates/public/style_guide.html:765
msgid "Details"
msgstr ""

#: ../flaskshop/templates/account/details.html:93
msgid "There are not any completed orders yet."
msgstr ""

#: ../flaskshop/templates/account/details.html:122
#: ../flaskshop/templates/dashboard/discount/sale.html:13
#: ../flaskshop/templates/dashboard/discount/voucher.html:14
#: ../flaskshop/templates/dashboard/list.html:45
#: ../flaskshop/templates/dashboard/product/attribute.html:14
#: ../flaskshop/templates/dashboard/product/category.html:14
#: ../flaskshop/templates/dashboard/product/collection.html:14
#: ../flaskshop/templates/dashboard/product/detail.html:16
#: ../flaskshop/templates/dashboard/product/detail.html:110
#: ../flaskshop/templates/dashboard/product/product_type.html:14
#: ../flaskshop/templates/dashboard/product/variant.html:14
#: ../flaskshop/templates/dashboard/site/shipping_method.html:14
#: ../flaskshop/templates/dashboard/site/site_menu.html:14
#: ../flaskshop/templates/dashboard/site/site_page.html:13
#: ../flaskshop/templates/dashboard/user/detail.html:31
#: ../flaskshop/templates/dashboard/user/detail.html:67
#: ../flaskshop/templates/public/style_guide.html:791
msgid "Edit"
msgstr ""

#: ../flaskshop/templates/account/details.html:135
#: ../flaskshop/templates/public/style_guide.html:802
msgid "Remove Address"
msgstr ""

#: ../flaskshop/templates/account/details.html:138
#: ../flaskshop/templates/public/style_guide.html:805
msgid "Cancel"
msgstr ""

#: ../flaskshop/templates/account/details.html:148
msgid "New Address"
msgstr ""

#: ../flaskshop/templates/account/login.html:10
msgid "Don't have an account yet?"
msgstr ""

#: ../flaskshop/templates/account/reser_passwd_mail.html:42
msgid "You reset your password"
msgstr ""

#: ../flaskshop/templates/account/reser_passwd_mail.html:46
msgid ""
"We cannot simply send you your old password. A unique\n"
"                                            password has been generated "
"for you. Change the password after logging in."
msgstr ""

#: ../flaskshop/templates/account/signup.html:10
msgid "Already have an account?"
msgstr ""

#: ../flaskshop/templates/account/signup.html:20
#: ../flaskshop/templates/account/signup.html:28
msgid "Create an account"
msgstr ""

#: ../flaskshop/templates/account/partials/login_form.html:21
msgid "Forgot password?"
msgstr ""

#: ../flaskshop/templates/checkout/_subtotal_table.html:5
#: ../flaskshop/templates/public/cart_dropdown.html:31
msgid "Shipment calculated at checkout"
msgstr ""

#: ../flaskshop/templates/checkout/cart.html:3
msgid "Your cart"
msgstr ""

#: ../flaskshop/templates/checkout/cart.html:8
msgid "Cart"
msgstr ""

#: ../flaskshop/templates/checkout/cart.html:15
msgid "Product has been removed from cart"
msgstr ""

#: ../flaskshop/templates/checkout/cart.html:75
#: ../flaskshop/templates/checkout/details.html:3
#: ../flaskshop/templates/checkout/details.html:16
#: ../flaskshop/templates/public/cart_dropdown.html:50
msgid "Checkout"
msgstr ""

#: ../flaskshop/templates/checkout/cart.html:82
#: ../flaskshop/templates/public/cart_dropdown.html:55
msgid "There are no products in your shopping cart."
msgstr ""

#: ../flaskshop/templates/checkout/cart.html:83
#: ../flaskshop/templates/public/cart_dropdown.html:57
msgid "Check out our sales"
msgstr ""

#: ../flaskshop/templates/checkout/details.html:8
msgid "Easy and secure!"
msgstr ""

#: ../flaskshop/templates/checkout/details.html:47
#: ../flaskshop/templates/dashboard/order/detail.html:92
#: ../flaskshop/templates/orders/_ordered_items_table.html:52
msgid "Subtotal"
msgstr ""

#: ../flaskshop/templates/checkout/details.html:57
msgid "Shipment"
msgstr ""

#: ../flaskshop/templates/checkout/details.html:72
msgid "Promo code"
msgstr ""

#: ../flaskshop/templates/checkout/details.html:78
msgid "Remove"
msgstr ""

#: ../flaskshop/templates/checkout/note.html:8
#: ../flaskshop/templates/checkout/shipping.html:8
#: ../flaskshop/templates/orders/_ordered_items_table.html:117
msgid "Shipping address"
msgstr ""

#: ../flaskshop/templates/checkout/note.html:10
msgid "Select other address"
msgstr ""

#: ../flaskshop/templates/checkout/note.html:15
msgid "Select other shipping method"
msgstr ""

#: ../flaskshop/templates/checkout/shipping.html:27
msgid "Enter a new address"
msgstr ""

#: ../flaskshop/templates/checkout/shipping.html:42
msgid "Shipping method"
msgstr ""

#: ../flaskshop/templates/checkout/shipping.html:60
msgid "Continue"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:17
msgid "Sales"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:18
msgid "today and"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:19
msgid "total"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:29
msgid "Members"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:30
msgid "new in"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:31
msgid "accounts"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:43
msgid "Orders are ready to fulfill"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:49
msgid "Payments to capture"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:55
msgid "products is on sale"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:64
msgid "Top Products"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:82
msgid "Ordered"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:98
msgid "Activity"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:105
msgid "was"
msgstr ""

#: ../flaskshop/templates/dashboard/index.html:110
msgid "There is no recent activity"
msgstr ""

#: ../flaskshop/templates/dashboard/layout.html:25
msgid "Front Site"
msgstr ""

#: ../flaskshop/templates/dashboard/discount/sale.html:13
#: ../flaskshop/templates/dashboard/discount/voucher.html:14
#: ../flaskshop/templates/dashboard/list.html:25
#: ../flaskshop/templates/dashboard/product/attribute.html:14
#: ../flaskshop/templates/dashboard/product/category.html:14
#: ../flaskshop/templates/dashboard/product/collection.html:14
#: ../flaskshop/templates/dashboard/product/detail.html:87
#: ../flaskshop/templates/dashboard/product/list.html:45
#: ../flaskshop/templates/dashboard/product/product_type.html:14
#: ../flaskshop/templates/dashboard/product/variant.html:14
#: ../flaskshop/templates/dashboard/site/shipping_method.html:14
#: ../flaskshop/templates/dashboard/site/site_menu.html:14
#: ../flaskshop/templates/dashboard/site/site_page.html:14
msgid "Create"
msgstr ""

#: ../flaskshop/templates/dashboard/list.html:36
#: ../flaskshop/templates/dashboard/order/list.html:44
#: ../flaskshop/templates/dashboard/product/detail.html:99
#: ../flaskshop/templates/dashboard/product/list.html:58
#: ../flaskshop/templates/dashboard/site/plugin.html:17
#: ../flaskshop/templates/dashboard/user/detail.html:58
#: ../flaskshop/templates/dashboard/user/detail.html:84
#: ../flaskshop/templates/dashboard/user/list.html:35
msgid "Operation"
msgstr ""

#: ../flaskshop/templates/dashboard/list.html:47
#: ../flaskshop/templates/dashboard/product/detail.html:18
#: ../flaskshop/templates/dashboard/product/detail.html:112
#: ../flaskshop/templates/dashboard/user/detail.html:33
msgid "Delete"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:23
msgid "Payment Status"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:24
msgid "Payment Due"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:29
msgid "Shipping Address"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:49
msgid "Qty"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:52
msgid "Unit Price"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:53
msgid "Subtotal)"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:81
msgid "Payment Method"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:82
msgid "Order Note"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:96
#: ../flaskshop/templates/orders/_ordered_items_table.html:69
msgid "Shipping"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:101
msgid "Discount"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:121
msgid "Send Order"
msgstr ""

#: ../flaskshop/templates/dashboard/order/detail.html:126
msgid "Draft Order"
msgstr ""

#: ../flaskshop/templates/dashboard/order/list.html:10
msgid "Order List"
msgstr ""

#: ../flaskshop/templates/dashboard/order/list.html:24
msgid "Order No."
msgstr ""

#: ../flaskshop/templates/dashboard/order/list.html:31
#: ../flaskshop/templates/dashboard/product/list.html:41
msgid "Query"
msgstr ""

#: ../flaskshop/templates/dashboard/order/list.html:53
#: ../flaskshop/templates/dashboard/product/list.html:67
#: ../flaskshop/templates/dashboard/user/detail.html:92
#: ../flaskshop/templates/dashboard/user/list.html:44
msgid "View"
msgstr ""

#: ../flaskshop/templates/dashboard/product/detail.html:62
msgid "Created"
msgstr ""

#: ../flaskshop/templates/dashboard/product/detail.html:68
msgid "Product Description"
msgstr ""

#: ../flaskshop/templates/dashboard/product/detail.html:83
msgid "Variants"
msgstr ""

#: ../flaskshop/templates/dashboard/product/detail.html:98
msgid "PriceOverride"
msgstr ""

#: ../flaskshop/templates/dashboard/product/list.html:12
msgid "Product List "
msgstr ""

#: ../flaskshop/templates/dashboard/product/list.html:20
msgid "Yes"
msgstr ""

#: ../flaskshop/templates/dashboard/product/list.html:21
msgid "No"
msgstr ""

#: ../flaskshop/templates/dashboard/product/product_create_step1.html:14
msgid "Create Product Step First"
msgstr ""

#: ../flaskshop/templates/dashboard/product/product_create_step2.html:12
msgid "Create Product Step 2"
msgstr ""

#: ../flaskshop/templates/dashboard/product/product_edit.html:14
msgid "Edit Product"
msgstr ""

#: ../flaskshop/templates/dashboard/product/variant.html:15
msgid "Variant"
msgstr ""

#: ../flaskshop/templates/dashboard/site/dashboard_menu.html:14
msgid "Create Dashboard Menu"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:7
msgid "Product Settings"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:14
msgid "Attributes"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:16
msgid "Determine attributes used to create product types"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:29
msgid "Define types of products you sell"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:40
msgid "Shipping Methods"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:42
msgid "Manage how you ship out orders"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:57
msgid "Pages"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:59
msgid "Manage and add additional pages"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:70
msgid "Navigations"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:72
msgid "Define how users can navigate through your store"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:83
msgid "Dashboard Navigations"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:85
msgid "Define how managers can navigate dashboard"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:96
msgid "Plugins"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:98
msgid "View and update your plugins and their settings."
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:109
msgid "Sites Settings"
msgstr ""

#: ../flaskshop/templates/dashboard/site/index.html:111
msgid "View and update your site settings"
msgstr ""

#: ../flaskshop/templates/dashboard/site/plugin.html:9
msgid "Manage Plugins"
msgstr ""

#: ../flaskshop/templates/dashboard/site/plugin.html:15
msgid "Plugin"
msgstr ""

#: ../flaskshop/templates/dashboard/site/plugin.html:16
msgid "Info"
msgstr ""

#: ../flaskshop/templates/dashboard/site/plugin.html:32
msgid "Disable"
msgstr ""

#: ../flaskshop/templates/dashboard/site/plugin.html:37
msgid "Enable"
msgstr ""

#: ../flaskshop/templates/dashboard/site/settings.html:13
msgid "Site Settings"
msgstr ""

#: ../flaskshop/templates/dashboard/site/settings.html:29
msgid "Save"
msgstr ""

#: ../flaskshop/templates/dashboard/site/site_menu.html:14
msgid "Site Menu"
msgstr ""

#: ../flaskshop/templates/dashboard/user/detail.html:45
msgid "Addresses"
msgstr ""

#: ../flaskshop/templates/dashboard/user/detail.html:46
msgid "Orders"
msgstr ""

#: ../flaskshop/templates/dashboard/user/detail.html:72
msgid "This user have not add address"
msgstr ""

#: ../flaskshop/templates/dashboard/user/detail.html:99
msgid "This user have not create orders"
msgstr ""

#: ../flaskshop/templates/dashboard/user/edit.html:13
msgid "Edit User"
msgstr ""

#: ../flaskshop/templates/dashboard/user/edit_addr.html:13
msgid "Edit User Address"
msgstr ""

#: ../flaskshop/templates/errors/404.html:4
msgid "Page Not Found"
msgstr ""

#: ../flaskshop/templates/errors/404.html:10
msgid "Sorry, that page doesn't exist."
msgstr ""

#: ../flaskshop/templates/errors/404.html:11
msgid "Want to"
msgstr ""

#: ../flaskshop/templates/errors/404.html:11
msgid "go home"
msgstr ""

#: ../flaskshop/templates/errors/404.html:11
msgid "instead"
msgstr ""

#: ../flaskshop/templates/orders/_ordered_items_table.html:124
msgid "Please pay before 00:30, else the order will auto closed"
msgstr ""

#: ../flaskshop/templates/orders/_ordered_items_table.html:126
msgid "Alipay"
msgstr ""

#: ../flaskshop/templates/orders/_ordered_items_table.html:129
msgid "Pay for test"
msgstr ""

#: ../flaskshop/templates/orders/_ordered_items_table.html:132
msgid "Cancel Order"
msgstr ""

#: ../flaskshop/templates/orders/_ordered_items_table.html:137
msgid "this order will auto received after 1 day"
msgstr ""

#: ../flaskshop/templates/orders/_ordered_items_table.html:139
msgid "Confirm receipt"
msgstr ""

#: ../flaskshop/templates/orders/checkout_success.html:14
msgid "Thank you for your"
msgstr ""

#: ../flaskshop/templates/orders/checkout_success.html:14
msgid "order"
msgstr ""

#: ../flaskshop/templates/orders/checkout_success.html:18
msgid "We've sent a confirmation email with details to"
msgstr ""

#: ../flaskshop/templates/orders/checkout_success.html:19
msgid "In case of any problems or questions please contact us"
msgstr ""

#: ../flaskshop/templates/orders/checkout_success.html:26
msgid "Continue shopping"
msgstr ""

#: ../flaskshop/templates/orders/details.html:36
msgid "Order Status"
msgstr ""

#: ../flaskshop/templates/orders/details.html:41
msgid "Your note"
msgstr ""

#: ../flaskshop/templates/products/_filters.html:64
msgid "Clear filters"
msgstr ""

#: ../flaskshop/templates/products/_filters.html:68
msgid "Update"
msgstr ""

#: ../flaskshop/templates/products/details.html:102
msgid "Please choose a variant!"
msgstr ""

#: ../flaskshop/templates/products/details.html:119
msgid "Add to cart"
msgstr ""

#: ../flaskshop/templates/products/details.html:131
msgid "This product is currently"
msgstr ""

#: ../flaskshop/templates/products/details.html:131
msgid "unavailable"
msgstr ""

#: ../flaskshop/templates/products/product_list_base.html:45
#: ../flaskshop/templates/public/style_guide.html:417
msgid "Filters"
msgstr ""

#: ../flaskshop/templates/products/product_list_base.html:53
#: ../flaskshop/templates/products/product_list_base.html:55
#: ../flaskshop/templates/products/product_list_base.html:72
msgid "Sort by"
msgstr ""

#: ../flaskshop/templates/products/product_list_base.html:78
msgid "ascending"
msgstr ""

#: ../flaskshop/templates/products/product_list_base.html:85
msgid "descending"
msgstr ""

#: ../flaskshop/templates/products/product_list_base.html:119
#: ../flaskshop/templates/public/search_result.html:23
msgid "Sorry, no matches found for your request."
msgstr ""

#: ../flaskshop/templates/products/product_list_base.html:120
#: ../flaskshop/templates/public/search_result.html:24
msgid "Try again or shop new arrivals."
msgstr ""

#: ../flaskshop/templates/public/home.html:11
msgid "Promo & Sale"
msgstr ""

#: ../flaskshop/templates/public/home.html:12
msgid "from the Northpole"
msgstr ""

#: ../flaskshop/templates/public/home.html:14
#: ../flaskshop/templates/public/home.html:29
#: ../flaskshop/templates/public/home.html:41
msgid "Shop now"
msgstr ""

#: ../flaskshop/templates/public/home.html:28
msgid "Size & Colours"
msgstr ""

#: ../flaskshop/templates/public/home.html:40
msgid "Digital Downloads"
msgstr ""

#: ../flaskshop/templates/public/home.html:50
msgid "Featured products"
msgstr ""

#: ../flaskshop/templates/public/page.html:27
msgid "Warning!"
msgstr ""

#: ../flaskshop/templates/public/page.html:28
msgid "You are previewing a page that is not visible."
msgstr ""

#: ../flaskshop/templates/public/search_result.html:3
msgid "Search results"
msgstr ""

#: ../flaskshop/templates/public/search_result.html:21
msgid "Search"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:13
msgid "Style guide"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:23
#: ../flaskshop/templates/public/style_guide.html:45
msgid "Typography"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:24
#: ../flaskshop/templates/public/style_guide.html:80
msgid "Colors"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:25
msgid "Grid"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:26
#: ../flaskshop/templates/public/style_guide.html:198
msgid "Responsive breakpoints"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:27
msgid "Helper classes"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:28
#: ../flaskshop/templates/public/style_guide.html:334
msgid "Icons"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:29
msgid "Buttons"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:30
#: ../flaskshop/templates/public/style_guide.html:395
msgid "Forms"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:31
#: ../flaskshop/templates/public/style_guide.html:588
msgid "Pagination"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:32
msgid "Breadcrumbs"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:33
#: ../flaskshop/templates/public/style_guide.html:624
msgid "Notifications"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:34
#: ../flaskshop/templates/public/style_guide.html:644
msgid "Main navigation"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:35
msgid "Tabs"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:36
msgid "Tables"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:37
msgid "Cards"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:38
msgid "Product items"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:39
#: ../flaskshop/templates/public/style_guide.html:852
msgid "Product gallery"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:47
msgid "We use Lato fontface in Regular(400),"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:48
msgid "and"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:48
msgid "Bold(700)"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:48
msgid "weights"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:49
msgid ""
"Lato includes only latin and latin extended characters. For other "
"alphabets the default sans serif font\n"
"              will be used."
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:52
#: ../flaskshop/templates/public/style_guide.html:708
#: ../flaskshop/templates/public/style_guide.html:775
#: ../flaskshop/templates/public/style_guide.html:817
msgid ""
"Click and edit any text below to see how the component looks with "
"different text"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:53
msgid "Headers"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:57
msgid "H1 example text"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:58
msgid "H2 example text"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:59
msgid "H3 example text (always uppercased)"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:62
msgid "Paragraph"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:65
msgid ""
"Saleor is open and free to use. It is not a bait to force you to pay us "
"later and\n"
"              we promise to do our bests to fix bugs and improve the "
"code. Some situations however call for a custom\n"
"              solution and extra code to be written. Whether you need us "
"to cover an exotic use case or build you a\n"
"              custom e-commerce appliance, our team can help."
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:71
msgid "Links"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:74
msgid "Link example text"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:75
msgid "Styled link example text"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:154
msgid "We use"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:155
msgid ""
"responsive grid which is based on a 12 column layout. In most cases we "
"use following combinations"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:206
msgid "Max container width"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:209
msgid "Class prefix"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:216
msgid "Extra small"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:219
msgid "None (auto)"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:229
msgid "Small"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:242
msgid "Medium"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:255
msgid "Large"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:268
msgid "Extra large"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:283
msgid "Storefront templates built with"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:285
msgid "framework wich has handful utility classes"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:286
msgid "Responsive floats"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:291
msgid "Float left on all viewport sizes"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:297
msgid "Float right on all viewport sizes"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:303
msgid "Don't float on all viewport sizes"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:309
msgid "Float left on viewports sized SM (small) or wider"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:315
msgid "Float right on viewports sized MD (medium) or wider"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:321
msgid "Responsive utilities"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:324
msgid ""
"The <code>.hidden-*-up</code> classes hide the element when the viewport "
"is at the given breakpoint or\n"
"              wider. For example, <code>.hidden-md-up</code> hides an "
"element on medium, large, and extra-large\n"
"              viewports."
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:327
msgid ""
"The <code>.hidden-*-down</code> classes hide the element when the "
"viewport is at the given breakpoint or\n"
"              smaller. For example, <code>.hidden-md-down</code> hides an"
" element on extra-small, small, and medium\n"
"              viewports."
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:351
msgid ""
"Click and edit any button below to see how the component looks with "
"different text"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:355
msgid "Primary Button (buttons can be really very wide)"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:358
msgid "Secondary Button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:361
msgid "Danger Button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:364
msgid "Disabled Button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:367
msgid "Gray Button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:370
msgid "Primary Narrow Button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:373
msgid "Secondary Narrow Button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:376
msgid "Danger Narrow Button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:384
msgid "Home page button"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:396
msgid "Text form"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:402
msgid "Given name"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:409
msgid "Family name"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:424
msgid "Box Size"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:431
#: ../flaskshop/templates/public/style_guide.html:527
msgid "100g"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:433
#: ../flaskshop/templates/public/style_guide.html:530
msgid "250g"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:435
msgid "500g"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:436
msgid "Feel free to place here as long option as you want"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:445
msgid "Select"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:452
#: ../flaskshop/templates/public/style_guide.html:453
#: ../flaskshop/templates/public/style_guide.html:454
#: ../flaskshop/templates/public/style_guide.html:455
msgid "Option"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:462
msgid "Number input"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:476
msgid "Radio buttons"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:484
#: ../flaskshop/templates/public/style_guide.html:559
msgid "First option"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:490
#: ../flaskshop/templates/public/style_guide.html:533
#: ../flaskshop/templates/public/style_guide.html:565
msgid "Feel free to place here as long text as you want"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:497
msgid "Input groups"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:516
msgid "Use"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:522
msgid "Variant picker"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:537
msgid "Errors"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:538
msgid ""
"Errors are always displayed as a small red text below the form explaining"
" what cause the error."
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:548
#: ../flaskshop/templates/public/style_guide.html:569
msgid "This field is required"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:555
msgid "Radio form"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:580
msgid "Only 32 remaining in stock"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:589
#: ../flaskshop/templates/public/style_guide.html:602
#: ../flaskshop/templates/public/style_guide.html:625
#: ../flaskshop/templates/public/style_guide.html:645
msgid ""
"Click and edit any item below to see how the component looks with "
"different text"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:630
msgid "This is Success alert"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:634
msgid "This is Danger alert"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:638
msgid ""
"This is Warning alert. Notifications content is not limited with "
"container width. Place here as many information as you want or need to"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:662
msgid "Tabs navigation"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:740
msgid "Payment pending"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:761
msgid "Fully paid"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:782
msgid "Mr. Walter C. Brown"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:783
msgid "49 Featherstone Street"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:784
msgid "London, EC1Y 8SY"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:786
msgid "United Kingdom"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:825
#: ../flaskshop/templates/public/style_guide.html:836
msgid "Johnson-Brown"
msgstr ""

#: ../flaskshop/templates/public/style_guide.html:843
msgid "FROM"
msgstr ""

#: ../plugin_example/conversations/forms.py:14
msgid "Recipient"
msgstr ""

#: ../plugin_example/conversations/forms.py:14
msgid "A valid username is required."
msgstr ""

#: ../plugin_example/conversations/forms.py:18
#: ../plugin_example/conversations/templates/conversation.html:252
msgid "Subject"
msgstr ""

#: ../plugin_example/conversations/forms.py:18
msgid "A Subject is required."
msgstr ""

#: ../plugin_example/conversations/forms.py:22
#: ../plugin_example/conversations/forms.py:53
msgid "Message"
msgstr ""

#: ../plugin_example/conversations/forms.py:22
#: ../plugin_example/conversations/forms.py:53
msgid "A message is required."
msgstr ""

#: ../plugin_example/conversations/forms.py:28
msgid "The username you entered does not exist."
msgstr ""

#: ../plugin_example/conversations/forms.py:30
msgid "You cannot send a PM to yourself."
msgstr ""

#: ../plugin_example/conversations/views.py:34
msgid ""
"You cannot send any messages anymore because you have reached your "
"message limit."
msgstr ""

#: ../plugin_example/conversations/templates/message_form.html:3
#: ../plugin_example/conversations/views.py:145
msgid "Compose Message"
msgstr ""

#: ../plugin_example/conversations/views.py:177
msgid "Message sent."
msgstr ""

#: ../plugin_example/conversations/templates/_inject_navlink.html:7
#: ../plugin_example/conversations/templates/_inject_navlink.html:18
#: ../plugin_example/conversations/templates/inbox.html:3
msgid "Inbox"
msgstr ""

#: ../plugin_example/conversations/templates/_inject_navlink.html:15
msgid "No unread messages."
msgstr ""

#: ../plugin_example/conversations/templates/_inject_navlink.html:19
#: ../plugin_example/conversations/templates/message_layout.html:20
msgid "New Message"
msgstr ""

#: ../plugin_example/conversations/templates/conversation.html:304
msgid "Send"
msgstr ""

#: ../plugin_example/conversations/templates/conversation_list.html:14
msgid "Conversations"
msgstr ""

#: ../plugin_example/conversations/templates/conversation_list.html:52
#: ../plugin_example/conversations/templates/conversation_list.html:59
msgid "Deleted User"
msgstr ""

#: ../plugin_example/conversations/templates/conversation_list.html:73
msgid "trash"
msgstr ""

#: ../plugin_example/conversations/templates/conversation_list.html:82
msgid "delete"
msgstr ""

#: ../plugin_example/conversations/templates/conversation_list.html:91
msgid "restore"
msgstr ""

#: ../plugin_example/conversations/templates/conversation_list.html:98
msgid "edit"
msgstr ""

#: ../plugin_example/conversations/templates/conversation_list.html:108
msgid "No conversations found."
msgstr ""

#: ../plugin_example/conversations/templates/message_layout.html:10
msgid "Private Message"
msgstr ""

#: ../plugin_example/conversations/templates/sent.html:3
msgid "Sent Messages"
msgstr ""

#: ../plugin_example/conversations/templates/trash.html:3
msgid "Trash"
msgstr ""
