# Flask Shop - An Amazing Project


## Introduction
This project is a front page copy of [saleor](https://github.com/mirumee/saleor) old version, but written with flask.

## ScreenShot

<table align="center">
    <tr>
        <td align="center">
            <a href="https://raw.githubusercontent.com/hjlarry/flask-shop/master/ScreenShot/1.png">
                <img src="ScreenShot/1.png" alt="Screenshot Home" width="300px" />
            </a>
        </td>
        <td align="center">
            <a href="https://raw.githubusercontent.com/hjlarry/flask-shop/master/ScreenShot/2.png">
                <img src="ScreenShot/2.png" alt="Screenshot Category" width="300px" />
            </a>
        </td>
        <td align="center">
            <a href="https://raw.githubusercontent.com/hjlarry/flask-shop/master/ScreenShot/3.png">
                <img src="ScreenShot/3.png" alt="Screenshot Cart" width="300px" />
            </a>
        </td>
    </tr>
    <tr>
        <td align="center">
            <a href="https://raw.githubusercontent.com/hjlarry/flask-shop/master/ScreenShot/4.png">
                <img src="ScreenShot/4.png" alt="Screenshot Admin Panel" width="300px" />
            </a>
        </td>
        <td align="center">
            <a href="https://raw.githubusercontent.com/hjlarry/flask-shop/master/ScreenShot/5.png">
                <img src="ScreenShot/5.png" alt="Screenshot Site Configuration" width="300px" />
            </a>
        </td>
        <td align="center">
            <a href="https://raw.githubusercontent.com/hjlarry/flask-shop/master/ScreenShot/6.png">
                <img src="ScreenShot/6.png" alt="Screenshot Order List" width="300px" />
            </a>
        </td>
    </tr>
</table>

## Feature

* large flask project structure, easy to extend and secondary develop
* always update and use newest packages, python features
* multi language and locallization support
* product ready, good performance


## Guide

Please see the [wiki](https://github.com/hjlarry/flask-shop/wiki) for more information:

* [How to install?](https://github.com/hjlarry/flask-shop/wiki/How-to-install-project%3F)
* [Project settings](https://github.com/hjlarry/flask-shop/wiki/Project-Settings)
* [Localization support](https://github.com/hjlarry/flask-shop/wiki/Localization-support)
* [Secondary development](https://github.com/hjlarry/flask-shop/wiki/Secondary-development)
