<style>
    .conversation-actions .btn{
        font-size:10px;
        margin-left:5px;
        text-transform:none;
    }
</style>

<div class="card">
    <div class="card-header">
        <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="float-left">
                    <span class="fa fa-comment"></span>{% trans %}Conversations{% endtrans %}
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <ul class="list-group list-group-flush">
        {% for conversation in conversations.items %}
        <li class="list-group-item {% if conversation.unread %}unread{% endif %}">
            <div class="row">
            <div class="col-2 conversation-avatar">
                {% if conversation.from_user and conversation.from_user.avatar %}
                <img src="{{ conversation.from_user.avatar }}" class="rounded-circle" alt="avatar" width="65px" height="65px" />
                {% else %}
                <img src="{{ url_for('static', filename='img/placeholder60x60.png') }}" class="rounded-circle" alt="avatar" width="65px" height="65px" />
                {% endif %}
            </div>
            <!-- other stuff -->
            <div class="col-10 conversation-info">
                <!-- subject -->
                <div class="conversation-subject">
                    <a href="{{url_for('conversations_bp.view_conversation', conversation_id=conversation.id)}}">
                        {% if conversation.unread %}
                            <strong>{{ conversation.subject }}</strong>
                        {% else %}
                            {{ conversation.subject }}
                        {% endif %}
                    </a>
                    <div class="float-right">
                        <strong><small># {{loop.index}}</small></strong>
                    </div>
                </div>
                <!-- meta info (date, user) -->
                <div class="conversation-meta">
                    From
                    {% if conversation.from_user %}
                        <a href="#">{{ conversation.from_user.username }}</a>
                    {% else %}
                        {% trans %}Deleted User{% endtrans %}
                    {% endif %}

                    to
                    {% if conversation.to_user %}
                        <a href="#">{{ conversation.to_user.username }}</a>
                    {% else %}
                        {% trans %}Deleted User{% endtrans %}
                    {% endif %}
                    on {{ conversation.last_message.created_at }}
                </div>
                <!-- actual content -->
                <div class="conversation-content">
                    {{ conversation.last_message.message }}
                </div>
                <!-- actions -->
                <div class="conversation-actions">
                    {% if include_move %}
                    <form class="inline-form float-right" method="POST" action="{{url_for('conversations_bp.move_conversation', conversation_id=conversation.id)}}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
                        <button type="submit" class="btn btn-info btn-xs" title="Move to Trash" data-toggle="tooltip" data-placement="top">
                            {% trans %}trash{% endtrans %}
                        </button>
                    </form>
                    {% endif %}

                    {% if include_delete %}
                    <form class="inline-form float-right" method="POST" action="{{url_for('conversations_bp.delete_conversation', conversation_id=conversation.id)}}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
                        <button type="submit" class="btn btn-danger btn-xs" title="Delete this conversation" data-toggle="tooltip" data-placement="top">
                            {% trans %}delete{% endtrans %}
                        </button>
                    </form>
                    {% endif %}

                    {% if include_restore %}
                    <form class="inline-form float-right" method="POST" action="{{url_for('conversations_bp.restore_conversation', conversation_id=conversation.id)}}">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
                        <button type="submit" class="btn btn-success btn-xs" title="Restore this conversation" data-toggle="tooltip" data-placement="top">
                            {% trans %}restore{% endtrans %}
                        </button>
                    </form>
                    {% endif %}

                    {% if include_edit %}
                    <a class="btn btn-success btn-xs float-right" alt="Edit" title="Edit this conversation" href="#" data-toggle="tooltip" data-placement="top">
                        {% trans %}edit{% endtrans %}
                    </a>
                    {% endif %}
                </div>
            </div>
            </div>
        </li>
        {% else %}
        <div class="row">
            <div class="col-md-12 col-sm-12 col-xs-12">
               {% trans %}No conversations found.{% endtrans %}
            </div>
        </div>
        {% endfor %}
        </ul>
    </div>
</div>
