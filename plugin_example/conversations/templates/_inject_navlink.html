{% macro is_active() %}
{% if request.endpoint.startswith("conversations_bp") %}active{% endif %}
{% endmacro %}

<li class="dropdown {{ is_active() }}">
    <a href="#" class="dropdown-toggle" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-expanded="false">
        <span class="fa fa-envelope fa-fw"></span>{% trans %}Inbox{% endtrans %}({{ unread_count }})
    </a>
    <div class="dropdown-menu" aria-labelledby="dropdownMenuLink">
        {% for message in unread_messages %}
        <a class="dropdown-item" href="{{ url_for('conversations_bp.view_conversation', conversation_id=message.id) }}">
            {{ message.from_user.username }}: {{ message.subject }}
        </a>
        {% else %}
        <a class="dropdown-item" href="#">{% trans %}No unread messages.{% endtrans %}</a>
        {% endfor %}
        <div class="dropdown-divider"></div>
        <a class="dropdown-item" href="{{ url_for('conversations_bp.inbox') }}">{% trans %}Inbox{% endtrans %}</a>
        <a class="dropdown-item" href="{{ url_for('conversations_bp.new_conversation') }}">{% trans %}New Message{%
            endtrans %}</a>
    </div>
</li>
