{% extends "message_layout.html" %}

{% block message_content %}
{# quick check if the conversation is a draft #}
{% if conversation.draft %}
{% set messages = [conversation.first_message] %}
{% else %}
{% set messages = conversation.messages %}
{% endif %}

<style>
  /*
 * Component: Direct Chat
 * ----------------------
 */
  .direct-chat .card-body {
    position: relative;
    overflow-x: hidden;
    padding: 0;
  }

  .direct-chat.chat-pane-open .direct-chat-contacts {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }

  .direct-chat-messages {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
    padding: 10px;
    height: 250px;
    overflow: auto;
  }

  .direct-chat-msg,
  .direct-chat-text {
    display: block;
  }

  .direct-chat-msg {
    margin-bottom: 10px;
  }

  .direct-chat-msg::after {
    display: block;
    clear: both;
    content: "";
  }

  .direct-chat-messages,
  .direct-chat-contacts {
    transition: transform .5s ease-in-out;
  }

  .direct-chat-text {
    border-radius: 0.3rem;
    position: relative;
    padding: 5px 10px;
    background: #d2d6de;
    border: 1px solid #d2d6de;
    margin: 5px 0 0 50px;
    color: #444;
  }

  .direct-chat-text:after,
  .direct-chat-text:before {
    position: absolute;
    right: 100%;
    top: 15px;
    border: solid transparent;
    border-right-color: #d2d6de;
    content: ' ';
    height: 0;
    width: 0;
    pointer-events: none;
  }

  .direct-chat-text:after {
    border-width: 5px;
    margin-top: -5px;
  }

  .direct-chat-text:before {
    border-width: 6px;
    margin-top: -6px;
  }

  .right .direct-chat-text {
    margin-right: 50px;
    margin-left: 0;
  }

  .right .direct-chat-text:after,
  .right .direct-chat-text:before {
    right: auto;
    left: 100%;
    border-right-color: transparent;
    border-left-color: #d2d6de;
  }

  .direct-chat-img {
    border-radius: 50%;
    float: left;
    width: 40px;
    height: 40px;
  }

  .right .direct-chat-img {
    float: right;
  }

  .direct-chat-info {
    display: block;
    margin-bottom: 2px;
    font-size: 0.875rem;
  }

  .direct-chat-name {
    font-weight: 600;
  }

  .direct-chat-timestamp {
    color: #999;
  }

  .direct-chat-contacts-open .direct-chat-contacts {
    -webkit-transform: translate(0, 0);
    -ms-transform: translate(0, 0);
    transform: translate(0, 0);
  }

  .direct-chat-contacts {
    -webkit-transform: translate(101%, 0);
    -ms-transform: translate(101%, 0);
    transform: translate(101%, 0);
    position: absolute;
    top: 0;
    bottom: 0;
    height: 250px;
    width: 100%;
    background: #222d32;
    color: #ffffff;
    overflow: auto;
  }

  .contacts-list>li {
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    padding: 10px;
    margin: 0;
  }

  .contacts-list>li::after {
    display: block;
    clear: both;
    content: "";
  }

  .contacts-list>li:last-of-type {
    border-bottom: none;
  }

  .contacts-list-img {
    border-radius: 50%;
    width: 40px;
    float: left;
  }

  .contacts-list-info {
    margin-left: 45px;
    color: #ffffff;
  }

  .contacts-list-name,
  .contacts-list-status {
    display: block;
  }

  .contacts-list-name {
    font-weight: 600;
  }

  .contacts-list-status {
    font-size: 0.875rem;
  }

  .contacts-list-date {
    color: #aaa;
    font-weight: normal;
  }

  .contacts-list-msg {
    color: #999;
  }

  .direct-chat-danger .right>.direct-chat-text {
    background: #dc3545;
    border-color: #dc3545;
    color: #ffffff;
  }

  .direct-chat-danger .right>.direct-chat-text:after,
  .direct-chat-danger .right>.direct-chat-text:before {
    border-left-color: #dc3545;
  }

  .direct-chat-primary .right>.direct-chat-text {
    background: #007bff;
    border-color: #007bff;
    color: #ffffff;
  }

  .direct-chat-primary .right>.direct-chat-text:after,
  .direct-chat-primary .right>.direct-chat-text:before {
    border-left-color: #007bff;
  }

  .direct-chat-warning .right>.direct-chat-text {
    background: #ffc107;
    border-color: #ffc107;
    color: #1F2D3D;
  }

  .direct-chat-warning .right>.direct-chat-text:after,
  .direct-chat-warning .right>.direct-chat-text:before {
    border-left-color: #ffc107;
  }

  .direct-chat-info .right>.direct-chat-text {
    background: #17a2b8;
    border-color: #17a2b8;
    color: #ffffff;
  }

  .direct-chat-info .right>.direct-chat-text:after,
  .direct-chat-info .right>.direct-chat-text:before {
    border-left-color: #17a2b8;
  }

  .direct-chat-success .right>.direct-chat-text {
    background: #28a745;
    border-color: #28a745;
    color: #ffffff;
  }

  .direct-chat-success .right>.direct-chat-text:after,
  .direct-chat-success .right>.direct-chat-text:before {
    border-left-color: #28a745;
  }

  .direct-chat .form-control {
    height: auto;
  }
</style>

<div class="card direct-chat direct-chat-primary">
  <div class="card-header">
    <h3 class="card-title">{% trans %}Subject{% endtrans %}: <strong>{{ conversation.subject }}</strong></h3>
  </div>
  <!-- /.card-header -->
  <div class="card-body">
    <!-- Conversations are loaded here -->
    <div class="direct-chat-messages">
      <!-- Message. Default to the left -->
      {% for message in messages %}
      {% if current_user.id != message.user_id %}
      <div class="direct-chat-msg">
        <div class="direct-chat-info clearfix">
          <span class="direct-chat-name float-left">{{ message.user.username }}</span>
          <span class="direct-chat-timestamp float-right">{{ message.created_at }}</span>
        </div>
        <!-- /.direct-chat-info -->
        <img class="direct-chat-img" src="{{ message.user.avatar }}" alt="message user image">
        <!-- /.direct-chat-img -->
        <div class="direct-chat-text">
          {{ message.message }}
        </div>
        <!-- /.direct-chat-text -->
      </div>
      {% endif %}
      {% if current_user.id == message.user_id %}
      <div class="direct-chat-msg right">
        <div class="direct-chat-info clearfix">
          <span class="direct-chat-name float-right">{{ message.user.username }}</span>
          <span class="direct-chat-timestamp float-left">{{ message.created_at }}</span>
        </div>
        <!-- /.direct-chat-info -->
        <img class="direct-chat-img" src="{{ message.user.avatar }}" alt="message user image">
        <!-- /.direct-chat-img -->
        <div class="direct-chat-text">
          {{ message.message }}
        </div>
        <!-- /.direct-chat-text -->
      </div>
      {% endif %}
      {% endfor %}
      <!-- /.direct-chat-msg -->

    </div>
  </div>
  <!-- /.card-body -->
  {% if not conversation.draft and conversation.from_user != None and conversation.to_user != None %}
  {% from 'bootstrap5/form.html' import render_field %}
  <div class="card-footer">
    <form action="#" method="post">
      {{ form.hidden_tag() }}
      <div class="input-group">
        <input type="text" name="message" id="message" placeholder="Type Message ..." class="form-control" required>
        <span class="input-group-append">
          <button type="submit" class="btn btn-primary">{% trans %}Send{% endtrans %}</button>
        </span>
      </div>
    </form>
  </div>
</div>
{% endif %}
{% endblock %}
