{% extends "message_layout.html" %}
{% from 'bootstrap5/form.html' import render_field %}
{% block title %} {% trans %}Compose Message{% endtrans %} {% endblock %}
{% block message_content %}

<form class="form-horizontal" role="form" method="post">
    {{ form.hidden_tag() }}
    <div class="card">
        <div class="card-header">
            {{ title }}
        </div>

        <div class="card-body">
            <div class="col-md-12 col-sm-12 col-xs-12">

                {{ render_field(form.to_user, div_class="col-md-6 col-sm-6 col-xs-6") }}
                {{ render_field(form.subject, div_class="col-md-6 col-sm-6 col-xs-6") }}
                {{ render_field(form.message, div_class="col-md-6 col-sm-6 col-xs-6") }}
                <div class="float-right">
                    <input class="btn btn-primary narrow" id="send_message" name="send_message" type="submit"
                        value="Start Conversation">
                </div>
            </div>
        </div>
    </div>
</form>
{% endblock %}
