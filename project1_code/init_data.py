# -*- coding: utf-8 -*-
"""
项目一：简易在线商城系统 - 数据库初始化脚本
用于创建示例数据
"""

from app import app, db, User, Admin, Category, Product, UserAddress
from werkzeug.security import generate_password_hash
import random

def init_database():
    """初始化数据库和示例数据"""
    with app.app_context():
        # 删除所有表
        db.drop_all()
        
        # 创建所有表
        db.create_all()
        
        print("数据库表创建完成")
        
        # 创建管理员账户
        create_admin_users()
        
        # 创建普通用户
        create_demo_users()
        
        # 创建商品分类
        create_categories()
        
        # 创建示例商品
        create_products()
        
        # 创建用户地址
        create_user_addresses()
        
        print("示例数据创建完成")

def create_admin_users():
    """创建管理员账户"""
    admin = Admin(
        username='admin',
        email='<EMAIL>',
        real_name='系统管理员'
    )
    admin.set_password('admin123')
    db.session.add(admin)
    
    print("管理员账户创建完成: admin/admin123")

def create_demo_users():
    """创建演示用户"""
    # 创建演示用户
    demo_user = User(
        username='demo',
        email='<EMAIL>',
        nickname='演示用户'
    )
    demo_user.set_password('demo123')
    db.session.add(demo_user)
    
    # 创建其他测试用户
    users_data = [
        {'username': 'user1', 'email': '<EMAIL>', 'nickname': '用户一'},
        {'username': 'user2', 'email': '<EMAIL>', 'nickname': '用户二'},
        {'username': 'user3', 'email': '<EMAIL>', 'nickname': '用户三'},
    ]
    
    for user_data in users_data:
        user = User(**user_data)
        user.set_password('123456')
        db.session.add(user)
    
    db.session.commit()
    print("演示用户创建完成: demo/demo123")

def create_categories():
    """创建商品分类"""
    categories_data = [
        {
            'name': '手机数码',
            'description': '智能手机、平板电脑、数码配件等',
            'sort_order': 1
        },
        {
            'name': '电脑办公',
            'description': '笔记本电脑、台式机、办公设备等',
            'sort_order': 2
        },
        {
            'name': '家用电器',
            'description': '冰箱、洗衣机、空调、小家电等',
            'sort_order': 3
        },
        {
            'name': '服装鞋帽',
            'description': '男装、女装、童装、鞋子、帽子等',
            'sort_order': 4
        },
        {
            'name': '图书音像',
            'description': '图书、音乐、影视、教育等',
            'sort_order': 5
        },
        {
            'name': '运动户外',
            'description': '运动装备、户外用品、健身器材等',
            'sort_order': 6
        }
    ]
    
    for category_data in categories_data:
        category = Category(**category_data)
        db.session.add(category)
    
    db.session.commit()
    print("商品分类创建完成")

def create_products():
    """创建示例商品"""
    categories = Category.query.all()
    
    # 手机数码类商品
    phone_products = [
        {
            'name': 'iPhone 15 Pro',
            'description': '苹果最新旗舰手机，搭载A17 Pro芯片，支持5G网络，拍照效果出色。',
            'price': 7999.00,
            'original_price': 8999.00,
            'stock': 50,
            'image_url': 'https://via.placeholder.com/300x300?text=iPhone+15+Pro',
            'is_featured': True,
            'sales_count': 120,
            'view_count': 1500
        },
        {
            'name': '华为Mate 60 Pro',
            'description': '华为旗舰手机，麒麟9000S芯片，超强拍照，长续航。',
            'price': 6999.00,
            'original_price': 7499.00,
            'stock': 30,
            'image_url': 'https://via.placeholder.com/300x300?text=Mate+60+Pro',
            'is_featured': True,
            'sales_count': 85,
            'view_count': 1200
        },
        {
            'name': '小米14',
            'description': '小米最新旗舰，骁龙8 Gen3芯片，徕卡影像，性价比之选。',
            'price': 3999.00,
            'stock': 80,
            'image_url': 'https://via.placeholder.com/300x300?text=小米14',
            'is_featured': False,
            'sales_count': 200,
            'view_count': 2000
        }
    ]
    
    # 电脑办公类商品
    computer_products = [
        {
            'name': 'MacBook Pro 14英寸',
            'description': '苹果专业笔记本，M3芯片，适合设计师和开发者使用。',
            'price': 14999.00,
            'original_price': 15999.00,
            'stock': 20,
            'image_url': 'https://via.placeholder.com/300x300?text=MacBook+Pro',
            'is_featured': True,
            'sales_count': 45,
            'view_count': 800
        },
        {
            'name': '联想ThinkPad X1',
            'description': '商务笔记本电脑，Intel i7处理器，轻薄便携，续航持久。',
            'price': 8999.00,
            'stock': 35,
            'image_url': 'https://via.placeholder.com/300x300?text=ThinkPad+X1',
            'is_featured': False,
            'sales_count': 60,
            'view_count': 600
        }
    ]
    
    # 家用电器类商品
    appliance_products = [
        {
            'name': '海尔冰箱 BCD-470WDPG',
            'description': '470升大容量冰箱，变频节能，智能控温，保鲜效果好。',
            'price': 3299.00,
            'original_price': 3799.00,
            'stock': 15,
            'image_url': 'https://via.placeholder.com/300x300?text=海尔冰箱',
            'is_featured': True,
            'sales_count': 25,
            'view_count': 400
        },
        {
            'name': '美的空调 KFR-35GW',
            'description': '1.5匹变频空调，一级能效，静音运行，快速制冷制热。',
            'price': 2199.00,
            'stock': 40,
            'image_url': 'https://via.placeholder.com/300x300?text=美的空调',
            'is_featured': False,
            'sales_count': 80,
            'view_count': 900
        }
    ]
    
    # 服装鞋帽类商品
    clothing_products = [
        {
            'name': 'Nike Air Max 270',
            'description': '耐克经典跑鞋，舒适透气，时尚百搭，适合日常穿着。',
            'price': 899.00,
            'original_price': 1099.00,
            'stock': 60,
            'image_url': 'https://via.placeholder.com/300x300?text=Nike+Air+Max',
            'is_featured': True,
            'sales_count': 150,
            'view_count': 1800
        },
        {
            'name': 'Adidas三叶草卫衣',
            'description': '阿迪达斯经典卫衣，纯棉面料，舒适保暖，经典三叶草标志。',
            'price': 399.00,
            'stock': 100,
            'image_url': 'https://via.placeholder.com/300x300?text=Adidas卫衣',
            'is_featured': False,
            'sales_count': 220,
            'view_count': 2200
        }
    ]
    
    # 图书音像类商品
    book_products = [
        {
            'name': '《Python编程从入门到实践》',
            'description': 'Python编程经典教程，适合初学者，内容详实，案例丰富。',
            'price': 89.00,
            'original_price': 109.00,
            'stock': 200,
            'image_url': 'https://via.placeholder.com/300x300?text=Python编程',
            'is_featured': False,
            'sales_count': 300,
            'view_count': 2500
        }
    ]
    
    # 运动户外类商品
    sports_products = [
        {
            'name': '哑铃套装 20KG',
            'description': '家用健身哑铃，可调节重量，适合力量训练，材质优良。',
            'price': 299.00,
            'stock': 50,
            'image_url': 'https://via.placeholder.com/300x300?text=哑铃套装',
            'is_featured': False,
            'sales_count': 80,
            'view_count': 600
        }
    ]
    
    # 将商品数据与分类关联
    all_products = [
        (phone_products, '手机数码'),
        (computer_products, '电脑办公'),
        (appliance_products, '家用电器'),
        (clothing_products, '服装鞋帽'),
        (book_products, '图书音像'),
        (sports_products, '运动户外')
    ]
    
    for products_list, category_name in all_products:
        category = Category.query.filter_by(name=category_name).first()
        if category:
            for product_data in products_list:
                product_data['category_id'] = category.id
                product = Product(**product_data)
                db.session.add(product)
    
    db.session.commit()
    print("示例商品创建完成")

def create_user_addresses():
    """创建用户地址"""
    demo_user = User.query.filter_by(username='demo').first()
    if demo_user:
        addresses_data = [
            {
                'user_id': demo_user.id,
                'receiver_name': '张三',
                'receiver_phone': '13800138000',
                'province': '北京市',
                'city': '北京市',
                'district': '朝阳区',
                'detail_address': '三里屯街道工体北路8号院',
                'is_default': True
            },
            {
                'user_id': demo_user.id,
                'receiver_name': '李四',
                'receiver_phone': '13900139000',
                'province': '上海市',
                'city': '上海市',
                'district': '浦东新区',
                'detail_address': '陆家嘴金融贸易区世纪大道100号',
                'is_default': False
            }
        ]
        
        for addr_data in addresses_data:
            address = UserAddress(**addr_data)
            db.session.add(address)
        
        db.session.commit()
        print("用户地址创建完成")

if __name__ == '__main__':
    init_database()
