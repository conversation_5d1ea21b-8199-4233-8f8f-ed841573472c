# 项目一：简易在线商城系统

## 项目简介

这是一个基于Flask开发的简易在线商城系统，实现了电商平台的核心功能，包括用户注册登录、商品展示、购物车管理、订单处理等。本项目适合作为Flask学习和电商系统开发的入门项目。

## 功能特性

### 前台功能
- ✅ 用户注册与登录
- ✅ 商品分类浏览
- ✅ 商品搜索
- ✅ 商品详情查看
- ✅ 购物车管理
- ✅ 订单创建与查看
- ✅ 用户地址管理
- ✅ 个人中心

### 后台功能
- ✅ 管理员登录
- ✅ 商品管理（增删改查）
- ✅ 分类管理
- ✅ 订单管理
- ✅ 用户管理

### API接口
- ✅ RESTful API设计
- ✅ 用户认证API
- ✅ 商品查询API
- ✅ 购物车操作API
- ✅ 订单管理API

## 技术栈

- **后端框架**: Flask 3.0
- **数据库**: SQLite（开发）/ MySQL（生产）
- **ORM**: SQLAlchemy
- **用户认证**: Flask-Login
- **前端**: HTML5 + CSS3 + JavaScript + Bootstrap 5
- **模板引擎**: Jinja2

## 项目结构

```
project1_code/
├── app.py                 # 主应用文件
├── init_data.py          # 数据库初始化脚本
├── requirements.txt      # Python依赖包
├── README.md            # 项目说明文档
├── templates/           # HTML模板文件
│   ├── base.html        # 基础模板
│   ├── index.html       # 首页
│   ├── login.html       # 登录页
│   ├── register.html    # 注册页
│   ├── products.html    # 商品列表
│   ├── product_detail.html  # 商品详情
│   ├── cart.html        # 购物车
│   ├── profile.html     # 个人中心
│   └── orders.html      # 订单列表
├── static/              # 静态文件（CSS、JS、图片）
└── instance/            # 实例文件夹（数据库文件）
```

## 快速开始

### 1. 环境要求

- Python 3.8+
- pip

### 2. 安装依赖

```bash
# 克隆项目
git clone <project-url>
cd project1_code

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 初始化数据库

```bash
# 运行数据库初始化脚本
python init_data.py
```

### 4. 启动应用

```bash
# 启动Flask应用
python app.py
```

应用将在 `http://localhost:5000` 启动。

### 5. 访问系统

#### 前台访问
- 首页: http://localhost:5000
- 商品列表: http://localhost:5000/products
- 用户登录: http://localhost:5000/login
- 用户注册: http://localhost:5000/register

#### 演示账户
- **普通用户**: demo / demo123
- **管理员**: admin / admin123

## API文档

### 基础信息
- **Base URL**: `http://localhost:5000/api/v1`
- **数据格式**: JSON
- **认证方式**: Session

### 主要接口

#### 用户认证
```
POST /api/v1/auth/register    # 用户注册
POST /api/v1/auth/login       # 用户登录
POST /api/v1/auth/logout      # 用户登出
GET  /api/v1/auth/profile     # 获取用户信息
```

#### 商品管理
```
GET  /api/v1/products         # 获取商品列表
GET  /api/v1/products/{id}    # 获取商品详情
GET  /api/v1/categories       # 获取分类列表
```

#### 购物车
```
GET    /api/v1/cart           # 获取购物车
POST   /api/v1/cart/items     # 添加商品到购物车
PUT    /api/v1/cart/items/{id} # 更新购物车商品
DELETE /api/v1/cart/items/{id} # 删除购物车商品
```

#### 订单管理
```
POST /api/v1/orders           # 创建订单
GET  /api/v1/orders           # 获取订单列表
GET  /api/v1/orders/{id}      # 获取订单详情
```

## 数据库设计

### 主要数据表

1. **users** - 用户表
2. **categories** - 商品分类表
3. **products** - 商品表
4. **cart_items** - 购物车表
5. **orders** - 订单表
6. **order_items** - 订单项表
7. **user_addresses** - 用户地址表
8. **admins** - 管理员表

详细的数据库设计请参考项目文档。

## 开发指南

### 添加新功能

1. **添加数据模型**: 在 `app.py` 中定义新的数据模型
2. **创建路由**: 添加新的路由处理函数
3. **创建模板**: 在 `templates/` 目录下创建HTML模板
4. **更新API**: 添加相应的API接口

### 自定义配置

可以通过修改 `app.py` 中的配置来自定义应用：

```python
# 数据库配置
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///shop.db'

# 安全配置
app.config['SECRET_KEY'] = 'your-secret-key'

# 调试模式
app.config['DEBUG'] = True
```

## 部署指南

### 生产环境部署

1. **使用MySQL数据库**:
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql://user:password@localhost/shop_db'
```

2. **使用Gunicorn**:
```bash
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:8000 app:app
```

3. **使用Nginx反向代理**:
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 测试

### 功能测试

1. **用户注册登录测试**
2. **商品浏览测试**
3. **购物车操作测试**
4. **订单流程测试**

### API测试

可以使用Postman或curl进行API测试：

```bash
# 用户登录
curl -X POST http://localhost:5000/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"demo","password":"demo123"}'

# 获取商品列表
curl http://localhost:5000/api/v1/products
```

## 常见问题

### Q: 如何重置数据库？
A: 删除 `instance/shop.db` 文件，然后重新运行 `python init_data.py`

### Q: 如何修改管理员密码？
A: 在 `init_data.py` 中修改管理员创建代码，然后重新初始化数据库

### Q: 如何添加新的商品分类？
A: 可以通过管理后台添加，或者在 `init_data.py` 中添加新分类

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式

- 项目作者: [Your Name]
- 邮箱: [<EMAIL>]
- 项目链接: [https://github.com/yourusername/project1_code](https://github.com/yourusername/project1_code)

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 实现基础电商功能
- 完成用户认证系统
- 实现商品管理
- 完成购物车和订单功能
