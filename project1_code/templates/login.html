{% extends "base.html" %}

{% block title %}用户登录 - 简易商城系统{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fas fa-sign-in-alt"></i> 用户登录</h4>
                </div>
                <div class="card-body">
                    <form method="POST" id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名/邮箱</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="请输入用户名或邮箱" required>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="请输入密码" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">记住我</label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="loginBtn">
                                <i class="fas fa-sign-in-alt"></i> 登录
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-0">还没有账户？ 
                            <a href="{{ url_for('register') }}" class="text-decoration-none">立即注册</a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- 演示账户信息 -->
            <div class="card mt-3">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-info-circle"></i> 演示账户</h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>用户名:</strong> demo</p>
                    <p class="mb-1"><strong>密码:</strong> demo123</p>
                    <p class="mb-0 text-muted small">您可以使用此账户进行演示</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 10px;
    }
    
    .card-header {
        border-radius: 10px 10px 0 0 !important;
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }
    
    .form-control {
        border-left: none;
    }
    
    .form-control:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px;
        font-weight: 500;
    }
    
    .btn-primary:hover {
        background: linear-gradient(45deg, #0056b3, #004085);
        transform: translateY(-1px);
    }
    
    .form-check-input:checked {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    a {
        color: #007bff;
        transition: color 0.3s ease;
    }
    
    a:hover {
        color: #0056b3;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 密码显示/隐藏切换
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('password');
        
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
        
        // 表单提交处理
        const loginForm = document.getElementById('loginForm');
        const loginBtn = document.getElementById('loginBtn');
        
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(loginForm);
            const originalBtnText = loginBtn.innerHTML;
            
            // 显示加载状态
            showLoading(loginBtn);
            
            // 提交表单
            fetch(loginForm.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // 如果响应包含错误信息，显示在页面上
                if (html.includes('alert-danger') || html.includes('alert-error')) {
                    document.body.innerHTML = html;
                } else {
                    // 登录成功，重定向
                    window.location.href = '{{ url_for("index") }}';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('登录失败，请重试');
            })
            .finally(() => {
                hideLoading(loginBtn, originalBtnText);
            });
        });
        
        // 演示账户快速填充
        const demoCard = document.querySelector('.card.mt-3');
        if (demoCard) {
            demoCard.addEventListener('click', function() {
                document.getElementById('username').value = 'demo';
                document.getElementById('password').value = 'demo123';
            });
            
            demoCard.style.cursor = 'pointer';
            demoCard.title = '点击快速填充演示账户信息';
        }
        
        // 输入框焦点效果
        const inputs = document.querySelectorAll('.form-control');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('shadow-sm');
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('shadow-sm');
            });
        });
    });
</script>
{% endblock %}
