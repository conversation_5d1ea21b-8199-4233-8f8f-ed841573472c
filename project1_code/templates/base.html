<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}简易商城系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
            color: #007bff !important;
        }
        
        .product-card {
            transition: transform 0.2s;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .product-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }
        
        .price {
            color: #e74c3c;
            font-weight: bold;
            font-size: 1.2em;
        }
        
        .original-price {
            color: #999;
            text-decoration: line-through;
            font-size: 0.9em;
        }
        
        .cart-badge {
            background-color: #e74c3c;
            color: white;
            border-radius: 50%;
            padding: 2px 6px;
            font-size: 0.8em;
            position: absolute;
            top: -8px;
            right: -8px;
        }
        
        .footer {
            background-color: #f8f9fa;
            padding: 40px 0;
            margin-top: 50px;
        }
        
        .alert {
            margin-bottom: 0;
        }
        
        .btn-primary {
            background-color: #007bff;
            border-color: #007bff;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
            border-color: #0056b3;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-light bg-light shadow-sm">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-store"></i> 简易商城
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('products') }}">商品</a>
                    </li>
                </ul>
                
                <!-- 搜索框 -->
                <form class="d-flex me-3" method="GET" action="{{ url_for('products') }}">
                    <input class="form-control me-2" type="search" name="keyword" placeholder="搜索商品..." 
                           value="{{ request.args.get('keyword', '') }}">
                    <button class="btn btn-outline-primary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
                
                <ul class="navbar-nav">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item">
                            <a class="nav-link position-relative" href="{{ url_for('cart') }}">
                                <i class="fas fa-shopping-cart"></i> 购物车
                                <span class="cart-badge" id="cart-count">0</span>
                            </a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" 
                               data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('profile') }}">个人中心</a></li>
                                <li><a class="dropdown-item" href="{{ url_for('orders') }}">我的订单</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('logout') }}">退出登录</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('login') }}">登录</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('register') }}">注册</a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 消息提示 -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}
    
    <!-- 主要内容 -->
    <main>
        {% block content %}{% endblock %}
    </main>
    
    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <h5>简易商城系统</h5>
                    <p class="text-muted">基于Flask开发的在线商城系统</p>
                </div>
                <div class="col-md-6">
                    <h5>联系我们</h5>
                    <p class="text-muted">
                        <i class="fas fa-envelope"></i> <EMAIL><br>
                        <i class="fas fa-phone"></i> ************
                    </p>
                </div>
            </div>
            <hr>
            <div class="row">
                <div class="col-12 text-center">
                    <p class="text-muted">&copy; 2024 简易商城系统. All rights reserved.</p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    <script>
        // 更新购物车数量
        function updateCartCount() {
            {% if current_user.is_authenticated %}
                fetch('/api/v1/cart')
                    .then(response => response.json())
                    .then(data => {
                        if (data.code === 200) {
                            document.getElementById('cart-count').textContent = data.data.total_quantity;
                        }
                    })
                    .catch(error => console.error('Error:', error));
            {% endif %}
        }
        
        // 页面加载时更新购物车数量
        document.addEventListener('DOMContentLoaded', function() {
            updateCartCount();
        });
        
        // 添加到购物车
        function addToCart(productId, quantity = 1) {
            {% if current_user.is_authenticated %}
                fetch('/api/v1/cart/items', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        product_id: productId,
                        quantity: quantity
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.code === 200) {
                        alert('添加成功！');
                        updateCartCount();
                    } else {
                        alert(data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('添加失败，请重试');
                });
            {% else %}
                alert('请先登录');
                window.location.href = '{{ url_for("login") }}';
            {% endif %}
        }
        
        // 显示加载状态
        function showLoading(element) {
            element.disabled = true;
            element.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
        }
        
        // 隐藏加载状态
        function hideLoading(element, originalText) {
            element.disabled = false;
            element.innerHTML = originalText;
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
