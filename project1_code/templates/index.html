{% extends "base.html" %}

{% block title %}首页 - 简易商城系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 轮播图 -->
    <div id="carouselExample" class="carousel slide mb-5" data-bs-ride="carousel">
        <div class="carousel-inner">
            <div class="carousel-item active">
                <div class="bg-primary text-white text-center py-5 rounded">
                    <h2>欢迎来到简易商城</h2>
                    <p class="lead">发现更多优质商品</p>
                    <a href="{{ url_for('products') }}" class="btn btn-light btn-lg">立即购物</a>
                </div>
            </div>
            <div class="carousel-item">
                <div class="bg-success text-white text-center py-5 rounded">
                    <h2>新品上市</h2>
                    <p class="lead">最新商品等你来发现</p>
                    <a href="{{ url_for('products') }}" class="btn btn-light btn-lg">查看新品</a>
                </div>
            </div>
            <div class="carousel-item">
                <div class="bg-warning text-white text-center py-5 rounded">
                    <h2>限时优惠</h2>
                    <p class="lead">精选商品特价销售</p>
                    <a href="{{ url_for('products') }}" class="btn btn-light btn-lg">抢购优惠</a>
                </div>
            </div>
        </div>
        <button class="carousel-control-prev" type="button" data-bs-target="#carouselExample" data-bs-slide="prev">
            <span class="carousel-control-prev-icon"></span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#carouselExample" data-bs-slide="next">
            <span class="carousel-control-next-icon"></span>
        </button>
    </div>
    
    <!-- 商品分类 -->
    {% if categories %}
    <section class="mb-5">
        <h3 class="mb-4">商品分类</h3>
        <div class="row">
            {% for category in categories %}
            <div class="col-md-3 col-sm-6 mb-3">
                <a href="{{ url_for('products', category_id=category.id) }}" class="text-decoration-none">
                    <div class="card h-100 text-center category-card">
                        <div class="card-body">
                            <i class="fas fa-tags fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">{{ category.name }}</h5>
                            <p class="card-text text-muted">{{ category.description or '精选商品' }}</p>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
    </section>
    {% endif %}
    
    <!-- 推荐商品 -->
    {% if products %}
    <section class="mb-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3>推荐商品</h3>
            <a href="{{ url_for('products') }}" class="btn btn-outline-primary">查看更多</a>
        </div>
        
        <div class="row">
            {% for product in products %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card product-card h-100">
                    <a href="{{ url_for('product_detail', product_id=product.id) }}" class="text-decoration-none">
                        {% if product.image_url %}
                            <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
                        {% else %}
                            <div class="card-img-top product-image bg-light d-flex align-items-center justify-content-center">
                                <i class="fas fa-image fa-3x text-muted"></i>
                            </div>
                        {% endif %}
                    </a>
                    
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title">
                            <a href="{{ url_for('product_detail', product_id=product.id) }}" 
                               class="text-decoration-none text-dark">
                                {{ product.name }}
                            </a>
                        </h6>
                        
                        <div class="mt-auto">
                            <div class="price-section mb-2">
                                <span class="price">¥{{ "%.2f"|format(product.price) }}</span>
                                {% if product.original_price and product.original_price > product.price %}
                                    <span class="original-price ms-2">¥{{ "%.2f"|format(product.original_price) }}</span>
                                {% endif %}
                            </div>
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <small class="text-muted">
                                    <i class="fas fa-eye"></i> {{ product.view_count }}
                                    <i class="fas fa-shopping-bag ms-2"></i> {{ product.sales_count }}
                                </small>
                                
                                {% if product.stock > 0 %}
                                    <button class="btn btn-primary btn-sm" 
                                            onclick="addToCart({{ product.id }})" 
                                            title="添加到购物车">
                                        <i class="fas fa-cart-plus"></i>
                                    </button>
                                {% else %}
                                    <span class="badge bg-secondary">缺货</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </section>
    {% else %}
    <section class="text-center py-5">
        <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
        <h4 class="text-muted">暂无推荐商品</h4>
        <p class="text-muted">请稍后再来查看</p>
    </section>
    {% endif %}
    
    <!-- 特色服务 -->
    <section class="mb-5">
        <h3 class="mb-4 text-center">我们的服务</h3>
        <div class="row">
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="text-center">
                    <i class="fas fa-shipping-fast fa-3x text-primary mb-3"></i>
                    <h5>快速配送</h5>
                    <p class="text-muted">全国包邮，快速到达</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="text-center">
                    <i class="fas fa-shield-alt fa-3x text-success mb-3"></i>
                    <h5>品质保证</h5>
                    <p class="text-muted">正品保障，质量可靠</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="text-center">
                    <i class="fas fa-headset fa-3x text-info mb-3"></i>
                    <h5>客服支持</h5>
                    <p class="text-muted">7x24小时在线客服</p>
                </div>
            </div>
            <div class="col-md-3 col-sm-6 mb-3">
                <div class="text-center">
                    <i class="fas fa-undo fa-3x text-warning mb-3"></i>
                    <h5>退换保障</h5>
                    <p class="text-muted">7天无理由退换货</p>
                </div>
            </div>
        </div>
    </section>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .category-card {
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
    }
    
    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        border-color: #007bff;
    }
    
    .carousel-item > div {
        min-height: 300px;
        display: flex;
        flex-direction: column;
        justify-content: center;
    }
    
    .product-card {
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .price {
        color: #e74c3c;
        font-weight: bold;
        font-size: 1.1em;
    }
    
    .original-price {
        color: #999;
        text-decoration: line-through;
        font-size: 0.9em;
    }
    
    @media (max-width: 768px) {
        .carousel-item > div {
            min-height: 200px;
        }
        
        .carousel-item h2 {
            font-size: 1.5rem;
        }
        
        .carousel-item .lead {
            font-size: 1rem;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // 自动播放轮播图
    document.addEventListener('DOMContentLoaded', function() {
        var carousel = new bootstrap.Carousel(document.getElementById('carouselExample'), {
            interval: 5000,
            wrap: true
        });
    });
</script>
{% endblock %}
