{% extends "base.html" %}

{% block title %}商品列表 - 简易商城系统{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 面包屑导航 -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">首页</a></li>
            <li class="breadcrumb-item active">商品列表</li>
        </ol>
    </nav>
    
    <div class="row">
        <!-- 侧边栏筛选 -->
        <div class="col-md-3">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-filter"></i> 商品筛选</h5>
                </div>
                <div class="card-body">
                    <!-- 分类筛选 -->
                    <div class="mb-4">
                        <h6 class="fw-bold">商品分类</h6>
                        <div class="list-group list-group-flush">
                            <a href="{{ url_for('products') }}" 
                               class="list-group-item list-group-item-action {% if not current_category %}active{% endif %}">
                                全部分类
                            </a>
                            {% for category in categories %}
                            <a href="{{ url_for('products', category_id=category.id) }}" 
                               class="list-group-item list-group-item-action {% if current_category == category.id %}active{% endif %}">
                                {{ category.name }}
                                <span class="badge bg-secondary float-end">{{ category.product_count }}</span>
                            </a>
                            {% endfor %}
                        </div>
                    </div>
                    
                    <!-- 价格筛选 -->
                    <div class="mb-4">
                        <h6 class="fw-bold">价格区间</h6>
                        <form method="GET" id="priceFilterForm">
                            {% if current_category %}
                                <input type="hidden" name="category_id" value="{{ current_category }}">
                            {% endif %}
                            {% if keyword %}
                                <input type="hidden" name="keyword" value="{{ keyword }}">
                            {% endif %}
                            
                            <div class="row g-2">
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           name="min_price" placeholder="最低价" 
                                           value="{{ request.args.get('min_price', '') }}">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" 
                                           name="max_price" placeholder="最高价"
                                           value="{{ request.args.get('max_price', '') }}">
                                </div>
                            </div>
                            <button type="submit" class="btn btn-primary btn-sm mt-2 w-100">筛选</button>
                        </form>
                    </div>
                    
                    <!-- 快速筛选 -->
                    <div class="mb-4">
                        <h6 class="fw-bold">快速筛选</h6>
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('products', featured=1) }}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-star"></i> 推荐商品
                            </a>
                            <a href="{{ url_for('products', sort='sales_desc') }}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-fire"></i> 热销商品
                            </a>
                            <a href="{{ url_for('products', sort='price_asc') }}" class="btn btn-outline-info btn-sm">
                                <i class="fas fa-sort-amount-up"></i> 价格从低到高
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 主要内容区 -->
        <div class="col-md-9">
            <!-- 搜索结果提示 -->
            {% if keyword %}
            <div class="alert alert-info">
                <i class="fas fa-search"></i> 搜索关键词："<strong>{{ keyword }}</strong>"，
                共找到 {{ pagination.total }} 个商品
            </div>
            {% endif %}
            
            <!-- 排序和视图切换 -->
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <span class="text-muted">共 {{ pagination.total }} 个商品</span>
                </div>
                
                <div class="d-flex align-items-center">
                    <!-- 排序选择 -->
                    <div class="me-3">
                        <select class="form-select form-select-sm" id="sortSelect" onchange="changeSorting()">
                            <option value="">默认排序</option>
                            <option value="price_asc" {% if request.args.get('sort') == 'price_asc' %}selected{% endif %}>
                                价格从低到高
                            </option>
                            <option value="price_desc" {% if request.args.get('sort') == 'price_desc' %}selected{% endif %}>
                                价格从高到低
                            </option>
                            <option value="sales_desc" {% if request.args.get('sort') == 'sales_desc' %}selected{% endif %}>
                                销量从高到低
                            </option>
                            <option value="view_desc" {% if request.args.get('sort') == 'view_desc' %}selected{% endif %}>
                                浏览量从高到低
                            </option>
                        </select>
                    </div>
                    
                    <!-- 视图切换 -->
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-secondary btn-sm active" id="gridView">
                            <i class="fas fa-th"></i>
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="listView">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 商品列表 -->
            {% if products %}
            <div id="productContainer">
                <div class="row" id="productGrid">
                    {% for product in products %}
                    <div class="col-lg-4 col-md-6 mb-4 product-item">
                        <div class="card product-card h-100">
                            <a href="{{ url_for('product_detail', product_id=product.id) }}" class="text-decoration-none">
                                {% if product.image_url %}
                                    <img src="{{ product.image_url }}" class="card-img-top product-image" alt="{{ product.name }}">
                                {% else %}
                                    <div class="card-img-top product-image bg-light d-flex align-items-center justify-content-center">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                {% endif %}
                            </a>
                            
                            <div class="card-body d-flex flex-column">
                                <h6 class="card-title">
                                    <a href="{{ url_for('product_detail', product_id=product.id) }}" 
                                       class="text-decoration-none text-dark">
                                        {{ product.name }}
                                    </a>
                                </h6>
                                
                                <p class="card-text text-muted small flex-grow-1">
                                    {{ product.description[:100] + '...' if product.description and product.description|length > 100 else product.description or '暂无描述' }}
                                </p>
                                
                                <div class="mt-auto">
                                    <div class="price-section mb-2">
                                        <span class="price">¥{{ "%.2f"|format(product.price) }}</span>
                                        {% if product.original_price and product.original_price > product.price %}
                                            <span class="original-price ms-2">¥{{ "%.2f"|format(product.original_price) }}</span>
                                        {% endif %}
                                    </div>
                                    
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <i class="fas fa-eye"></i> {{ product.view_count }}
                                            <i class="fas fa-shopping-bag ms-2"></i> {{ product.sales_count }}
                                        </small>
                                        
                                        {% if product.stock > 0 %}
                                            <button class="btn btn-primary btn-sm" 
                                                    onclick="addToCart({{ product.id }})" 
                                                    title="添加到购物车">
                                                <i class="fas fa-cart-plus"></i>
                                            </button>
                                        {% else %}
                                            <span class="badge bg-secondary">缺货</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- 分页 -->
            {% if pagination.pages > 1 %}
            <nav aria-label="商品分页">
                <ul class="pagination justify-content-center">
                    {% if pagination.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=pagination.prev_num, **request.args) }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    {% endif %}
                    
                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('products', page=page_num, **request.args) }}">
                                        {{ page_num }}
                                    </a>
                                </li>
                            {% else %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                            {% endif %}
                        {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">…</span>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=pagination.next_num, **request.args) }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
            
            {% else %}
            <!-- 无商品提示 -->
            <div class="text-center py-5">
                <i class="fas fa-box-open fa-4x text-muted mb-3"></i>
                <h4 class="text-muted">暂无商品</h4>
                <p class="text-muted">
                    {% if keyword %}
                        没有找到与"{{ keyword }}"相关的商品
                    {% else %}
                        该分类下暂无商品
                    {% endif %}
                </p>
                <a href="{{ url_for('products') }}" class="btn btn-primary">查看全部商品</a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .product-card {
        transition: all 0.3s ease;
        border: 1px solid #dee2e6;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    
    .product-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
    }
    
    .price {
        color: #e74c3c;
        font-weight: bold;
        font-size: 1.1em;
    }
    
    .original-price {
        color: #999;
        text-decoration: line-through;
        font-size: 0.9em;
    }
    
    .list-group-item.active {
        background-color: #007bff;
        border-color: #007bff;
    }
    
    .btn-group .btn.active {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }
    
    .product-list-view .product-item {
        width: 100%;
    }
    
    .product-list-view .card {
        flex-direction: row;
    }
    
    .product-list-view .product-image {
        width: 200px;
        height: 150px;
    }
    
    .breadcrumb {
        background-color: transparent;
        padding: 0;
    }
    
    .pagination .page-link {
        color: #007bff;
    }
    
    .pagination .page-item.active .page-link {
        background-color: #007bff;
        border-color: #007bff;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    // 排序功能
    function changeSorting() {
        const sortValue = document.getElementById('sortSelect').value;
        const url = new URL(window.location);
        
        if (sortValue) {
            url.searchParams.set('sort', sortValue);
        } else {
            url.searchParams.delete('sort');
        }
        
        window.location.href = url.toString();
    }
    
    // 视图切换
    document.addEventListener('DOMContentLoaded', function() {
        const gridView = document.getElementById('gridView');
        const listView = document.getElementById('listView');
        const productContainer = document.getElementById('productContainer');
        
        gridView.addEventListener('click', function() {
            gridView.classList.add('active');
            listView.classList.remove('active');
            productContainer.classList.remove('product-list-view');
        });
        
        listView.addEventListener('click', function() {
            listView.classList.add('active');
            gridView.classList.remove('active');
            productContainer.classList.add('product-list-view');
        });
    });
</script>
{% endblock %}
