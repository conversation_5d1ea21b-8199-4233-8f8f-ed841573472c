{% extends "base.html" %}

{% block title %}用户注册 - 简易商城系统{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow">
                <div class="card-header bg-success text-white text-center">
                    <h4><i class="fas fa-user-plus"></i> 用户注册</h4>
                </div>
                <div class="card-body">
                    <form method="POST" id="registerForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">用户名 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="3-20个字符，字母数字下划线" required
                                       pattern="[a-zA-Z0-9_]{3,20}" title="用户名只能包含字母、数字和下划线，长度3-20个字符">
                            </div>
                            <div class="form-text">用户名将作为您的登录凭证</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email" name="email" 
                                       placeholder="请输入有效的邮箱地址" required>
                            </div>
                            <div class="form-text">邮箱可用于登录和找回密码</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">密码 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="6-20个字符" required
                                       minlength="6" maxlength="20">
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="password-strength">
                                <div class="progress mt-2" style="height: 5px;">
                                    <div class="progress-bar" id="passwordStrength" role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="form-text" id="passwordStrengthText">密码强度：无</small>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">确认密码 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" 
                                       placeholder="请再次输入密码" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="form-text" id="passwordMatch"></div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="agree_terms" required>
                            <label class="form-check-label" for="agree_terms">
                                我已阅读并同意 <a href="#" class="text-decoration-none">用户协议</a> 和 
                                <a href="#" class="text-decoration-none">隐私政策</a>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success" id="registerBtn">
                                <i class="fas fa-user-plus"></i> 立即注册
                            </button>
                        </div>
                    </form>
                    
                    <hr>
                    
                    <div class="text-center">
                        <p class="mb-0">已有账户？ 
                            <a href="{{ url_for('login') }}" class="text-decoration-none">立即登录</a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        border: none;
        border-radius: 10px;
    }
    
    .card-header {
        border-radius: 10px 10px 0 0 !important;
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-right: none;
    }
    
    .form-control {
        border-left: none;
    }
    
    .form-control:focus {
        border-color: #28a745;
        box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    }
    
    .btn-success {
        background: linear-gradient(45deg, #28a745, #20c997);
        border: none;
        padding: 12px;
        font-weight: 500;
    }
    
    .btn-success:hover {
        background: linear-gradient(45deg, #20c997, #17a2b8);
        transform: translateY(-1px);
    }
    
    .form-check-input:checked {
        background-color: #28a745;
        border-color: #28a745;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
    
    .password-strength .progress-bar {
        transition: all 0.3s ease;
    }
    
    .strength-weak {
        background-color: #dc3545 !important;
    }
    
    .strength-medium {
        background-color: #ffc107 !important;
    }
    
    .strength-strong {
        background-color: #28a745 !important;
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.875em;
    }
    
    .is-valid {
        border-color: #28a745;
    }
    
    .is-invalid {
        border-color: #dc3545;
    }
    
    a {
        color: #28a745;
        transition: color 0.3s ease;
    }
    
    a:hover {
        color: #20c997;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 密码显示/隐藏切换
        function setupPasswordToggle(toggleId, inputId) {
            const toggle = document.getElementById(toggleId);
            const input = document.getElementById(inputId);
            
            toggle.addEventListener('click', function() {
                const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                input.setAttribute('type', type);
                
                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }
        
        setupPasswordToggle('togglePassword', 'password');
        setupPasswordToggle('toggleConfirmPassword', 'confirm_password');
        
        // 密码强度检测
        const passwordInput = document.getElementById('password');
        const strengthBar = document.getElementById('passwordStrength');
        const strengthText = document.getElementById('passwordStrengthText');
        
        passwordInput.addEventListener('input', function() {
            const password = this.value;
            const strength = calculatePasswordStrength(password);
            
            strengthBar.style.width = strength.percentage + '%';
            strengthBar.className = 'progress-bar ' + strength.class;
            strengthText.textContent = '密码强度：' + strength.text;
        });
        
        function calculatePasswordStrength(password) {
            let score = 0;
            
            if (password.length >= 6) score += 1;
            if (password.length >= 10) score += 1;
            if (/[a-z]/.test(password)) score += 1;
            if (/[A-Z]/.test(password)) score += 1;
            if (/[0-9]/.test(password)) score += 1;
            if (/[^A-Za-z0-9]/.test(password)) score += 1;
            
            if (score < 3) {
                return { percentage: 33, class: 'strength-weak', text: '弱' };
            } else if (score < 5) {
                return { percentage: 66, class: 'strength-medium', text: '中等' };
            } else {
                return { percentage: 100, class: 'strength-strong', text: '强' };
            }
        }
        
        // 确认密码验证
        const confirmPasswordInput = document.getElementById('confirm_password');
        const passwordMatchText = document.getElementById('passwordMatch');
        
        function checkPasswordMatch() {
            const password = passwordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            if (confirmPassword === '') {
                passwordMatchText.textContent = '';
                confirmPasswordInput.classList.remove('is-valid', 'is-invalid');
                return;
            }
            
            if (password === confirmPassword) {
                passwordMatchText.textContent = '✓ 密码匹配';
                passwordMatchText.className = 'form-text text-success';
                confirmPasswordInput.classList.remove('is-invalid');
                confirmPasswordInput.classList.add('is-valid');
            } else {
                passwordMatchText.textContent = '✗ 密码不匹配';
                passwordMatchText.className = 'form-text text-danger';
                confirmPasswordInput.classList.remove('is-valid');
                confirmPasswordInput.classList.add('is-invalid');
            }
        }
        
        passwordInput.addEventListener('input', checkPasswordMatch);
        confirmPasswordInput.addEventListener('input', checkPasswordMatch);
        
        // 表单提交处理
        const registerForm = document.getElementById('registerForm');
        const registerBtn = document.getElementById('registerBtn');
        
        registerForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 验证密码匹配
            if (passwordInput.value !== confirmPasswordInput.value) {
                alert('两次密码输入不一致');
                return;
            }
            
            const formData = new FormData(registerForm);
            const originalBtnText = registerBtn.innerHTML;
            
            // 显示加载状态
            showLoading(registerBtn);
            
            // 提交表单
            fetch(registerForm.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // 如果响应包含错误信息，显示在页面上
                if (html.includes('alert-danger') || html.includes('alert-error')) {
                    document.body.innerHTML = html;
                } else {
                    // 注册成功，重定向到登录页
                    window.location.href = '{{ url_for("login") }}';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('注册失败，请重试');
            })
            .finally(() => {
                hideLoading(registerBtn, originalBtnText);
            });
        });
        
        // 输入框实时验证
        const usernameInput = document.getElementById('username');
        const emailInput = document.getElementById('email');
        
        usernameInput.addEventListener('blur', function() {
            const username = this.value;
            if (username && !/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                if (username) this.classList.add('is-valid');
            }
        });
        
        emailInput.addEventListener('blur', function() {
            const email = this.value;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (email && !emailRegex.test(email)) {
                this.classList.add('is-invalid');
            } else {
                this.classList.remove('is-invalid');
                if (email) this.classList.add('is-valid');
            }
        });
    });
</script>
{% endblock %}
