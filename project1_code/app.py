# -*- coding: utf-8 -*-
"""
项目一：简易在线商城系统 - 主应用文件
Flask应用入口文件
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
import os

# 创建Flask应用实例
app = Flask(__name__)

# 应用配置
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///shop.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化扩展
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = '请先登录'

# 数据库模型定义
class User(UserMixin, db.Model):
    """用户模型"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    nickname = db.Column(db.String(50))
    phone = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    cart_items = db.relationship('CartItem', backref='user', lazy=True, cascade='all, delete-orphan')
    orders = db.relationship('Order', backref='user', lazy=True)
    addresses = db.relationship('UserAddress', backref='user', lazy=True, cascade='all, delete-orphan')
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'nickname': self.nickname,
            'phone': self.phone,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class Category(db.Model):
    """商品分类模型"""
    __tablename__ = 'categories'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    parent_id = db.Column(db.Integer, db.ForeignKey('categories.id'))
    sort_order = db.Column(db.Integer, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关系
    products = db.relationship('Product', backref='category', lazy=True)
    children = db.relationship('Category', backref=db.backref('parent', remote_side=[id]))
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'parent_id': self.parent_id,
            'product_count': len(self.products)
        }

class Product(db.Model):
    """商品模型"""
    __tablename__ = 'products'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    price = db.Column(db.Numeric(10, 2), nullable=False)
    original_price = db.Column(db.Numeric(10, 2))
    stock = db.Column(db.Integer, default=0)
    category_id = db.Column(db.Integer, db.ForeignKey('categories.id'), nullable=False)
    image_url = db.Column(db.String(255))
    sales_count = db.Column(db.Integer, default=0)
    view_count = db.Column(db.Integer, default=0)
    is_featured = db.Column(db.Boolean, default=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    cart_items = db.relationship('CartItem', backref='product', lazy=True)
    order_items = db.relationship('OrderItem', backref='product', lazy=True)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'price': float(self.price),
            'original_price': float(self.original_price) if self.original_price else None,
            'stock': self.stock,
            'category_id': self.category_id,
            'image_url': self.image_url,
            'sales_count': self.sales_count,
            'view_count': self.view_count,
            'is_featured': self.is_featured,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class CartItem(db.Model):
    """购物车项模型"""
    __tablename__ = 'cart_items'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    quantity = db.Column(db.Integer, default=1)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('user_id', 'product_id', name='uk_user_product'),)
    
    @property
    def subtotal(self):
        """计算小计"""
        return float(self.product.price * self.quantity)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'product': self.product.to_dict(),
            'quantity': self.quantity,
            'subtotal': self.subtotal
        }

class Order(db.Model):
    """订单模型"""
    __tablename__ = 'orders'
    
    id = db.Column(db.Integer, primary_key=True)
    order_no = db.Column(db.String(32), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)
    status = db.Column(db.Integer, default=1)  # 1:待支付 2:待发货 3:已发货 4:已完成 5:已取消
    receiver_name = db.Column(db.String(50), nullable=False)
    receiver_phone = db.Column(db.String(20), nullable=False)
    receiver_address = db.Column(db.String(500), nullable=False)
    remark = db.Column(db.String(500))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 关系
    items = db.relationship('OrderItem', backref='order', lazy=True, cascade='all, delete-orphan')
    
    @property
    def status_text(self):
        """订单状态文本"""
        status_map = {
            1: '待支付',
            2: '待发货', 
            3: '已发货',
            4: '已完成',
            5: '已取消'
        }
        return status_map.get(self.status, '未知')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_no': self.order_no,
            'total_amount': float(self.total_amount),
            'status': self.status,
            'status_text': self.status_text,
            'receiver_name': self.receiver_name,
            'receiver_phone': self.receiver_phone,
            'receiver_address': self.receiver_address,
            'remark': self.remark,
            'item_count': len(self.items),
            'created_at': self.created_at.isoformat() if self.created_at else None
        }

class OrderItem(db.Model):
    """订单项模型"""
    __tablename__ = 'order_items'
    
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('orders.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('products.id'), nullable=False)
    product_name = db.Column(db.String(200), nullable=False)
    product_image = db.Column(db.String(255))
    price = db.Column(db.Numeric(10, 2), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    total_amount = db.Column(db.Numeric(10, 2), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'product_name': self.product_name,
            'product_image': self.product_image,
            'price': float(self.price),
            'quantity': self.quantity,
            'total_amount': float(self.total_amount)
        }

class UserAddress(db.Model):
    """用户地址模型"""
    __tablename__ = 'user_addresses'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    receiver_name = db.Column(db.String(50), nullable=False)
    receiver_phone = db.Column(db.String(20), nullable=False)
    province = db.Column(db.String(50), nullable=False)
    city = db.Column(db.String(50), nullable=False)
    district = db.Column(db.String(50), nullable=False)
    detail_address = db.Column(db.String(200), nullable=False)
    is_default = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'receiver_name': self.receiver_name,
            'receiver_phone': self.receiver_phone,
            'province': self.province,
            'city': self.city,
            'district': self.district,
            'detail_address': self.detail_address,
            'is_default': self.is_default
        }

# 管理员模型
class Admin(UserMixin, db.Model):
    """管理员模型"""
    __tablename__ = 'admins'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    real_name = db.Column(db.String(50))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def set_password(self, password):
        """设置密码"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """验证密码"""
        return check_password_hash(self.password_hash, password)

# Flask-Login用户加载器
@login_manager.user_loader
def load_user(user_id):
    """加载用户"""
    return User.query.get(int(user_id))

# 工具函数
def generate_order_no():
    """生成订单号"""
    import time
    import random
    timestamp = str(int(time.time()))
    random_num = str(random.randint(1000, 9999))
    return f"ORD{timestamp}{random_num}"

def api_response(code=200, message="success", data=None):
    """API响应格式"""
    return jsonify({
        'code': code,
        'message': message,
        'data': data,
        'timestamp': datetime.utcnow().isoformat() + 'Z'
    })

# 创建数据库表
with app.app_context():
    db.create_all()
    
    # 创建默认管理员账户
    admin = Admin.query.filter_by(username='admin').first()
    if not admin:
        admin = Admin(
            username='admin',
            email='<EMAIL>',
            real_name='系统管理员'
        )
        admin.set_password('admin123')
        db.session.add(admin)
        db.session.commit()
        print("默认管理员账户已创建: admin/admin123")

# ==================== 前台路由 ====================

@app.route('/')
def index():
    """首页"""
    # 获取推荐商品
    featured_products = Product.query.filter_by(is_featured=True, is_active=True).limit(8).all()
    # 获取分类
    categories = Category.query.filter_by(is_active=True, parent_id=None).all()
    return render_template('index.html', products=featured_products, categories=categories)

@app.route('/register', methods=['GET', 'POST'])
def register():
    """用户注册"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')

        # 验证输入
        if not all([username, email, password, confirm_password]):
            flash('请填写完整信息', 'error')
            return render_template('register.html')

        if password != confirm_password:
            flash('两次密码输入不一致', 'error')
            return render_template('register.html')

        # 检查用户名和邮箱是否已存在
        if User.query.filter_by(username=username).first():
            flash('用户名已存在', 'error')
            return render_template('register.html')

        if User.query.filter_by(email=email).first():
            flash('邮箱已存在', 'error')
            return render_template('register.html')

        # 创建用户
        user = User(username=username, email=email)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()

        flash('注册成功，请登录', 'success')
        return redirect(url_for('login'))

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """用户登录"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not all([username, password]):
            flash('请输入用户名和密码', 'error')
            return render_template('login.html')

        # 查找用户（支持用户名或邮箱登录）
        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()

        if user and user.check_password(password):
            login_user(user)
            flash('登录成功', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('用户名或密码错误', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """用户登出"""
    logout_user()
    flash('已退出登录', 'info')
    return redirect(url_for('index'))

@app.route('/products')
def products():
    """商品列表"""
    page = request.args.get('page', 1, type=int)
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    query = Product.query.filter_by(is_active=True)

    # 分类筛选
    if category_id:
        query = query.filter_by(category_id=category_id)

    # 关键词搜索
    if keyword:
        query = query.filter(Product.name.contains(keyword))

    # 分页
    pagination = query.paginate(page=page, per_page=20, error_out=False)
    products = pagination.items

    # 获取分类列表
    categories = Category.query.filter_by(is_active=True).all()

    return render_template('products.html',
                         products=products,
                         pagination=pagination,
                         categories=categories,
                         current_category=category_id,
                         keyword=keyword)

@app.route('/product/<int:product_id>')
def product_detail(product_id):
    """商品详情"""
    product = Product.query.get_or_404(product_id)

    # 增加浏览次数
    product.view_count += 1
    db.session.commit()

    return render_template('product_detail.html', product=product)

@app.route('/cart')
@login_required
def cart():
    """购物车"""
    cart_items = CartItem.query.filter_by(user_id=current_user.id).all()
    total_amount = sum(item.subtotal for item in cart_items)
    return render_template('cart.html', cart_items=cart_items, total_amount=total_amount)

@app.route('/profile')
@login_required
def profile():
    """个人中心"""
    # 获取用户订单
    orders = Order.query.filter_by(user_id=current_user.id).order_by(Order.created_at.desc()).limit(5).all()
    return render_template('profile.html', orders=orders)

@app.route('/orders')
@login_required
def orders():
    """订单列表"""
    page = request.args.get('page', 1, type=int)
    pagination = Order.query.filter_by(user_id=current_user.id)\
                           .order_by(Order.created_at.desc())\
                           .paginate(page=page, per_page=10, error_out=False)
    orders = pagination.items
    return render_template('orders.html', orders=orders, pagination=pagination)

@app.route('/order/<int:order_id>')
@login_required
def order_detail(order_id):
    """订单详情"""
    order = Order.query.filter_by(id=order_id, user_id=current_user.id).first_or_404()
    return render_template('order_detail.html', order=order)

# ==================== API路由 ====================

@app.route('/api/v1/auth/register', methods=['POST'])
def api_register():
    """API: 用户注册"""
    data = request.get_json()

    username = data.get('username')
    email = data.get('email')
    password = data.get('password')
    confirm_password = data.get('confirm_password')

    # 验证输入
    if not all([username, email, password, confirm_password]):
        return api_response(400, '请填写完整信息')

    if password != confirm_password:
        return api_response(400, '两次密码输入不一致')

    # 检查用户名和邮箱是否已存在
    if User.query.filter_by(username=username).first():
        return api_response(1001, '用户名已存在')

    if User.query.filter_by(email=email).first():
        return api_response(1002, '邮箱已存在')

    # 创建用户
    user = User(username=username, email=email)
    user.set_password(password)
    db.session.add(user)
    db.session.commit()

    return api_response(200, '注册成功', user.to_dict())

@app.route('/api/v1/auth/login', methods=['POST'])
def api_login():
    """API: 用户登录"""
    data = request.get_json()

    username = data.get('username')
    password = data.get('password')

    if not all([username, password]):
        return api_response(400, '请输入用户名和密码')

    # 查找用户
    user = User.query.filter(
        (User.username == username) | (User.email == username)
    ).first()

    if user and user.check_password(password):
        login_user(user)
        return api_response(200, '登录成功', user.to_dict())
    else:
        return api_response(1003, '用户名或密码错误')

@app.route('/api/v1/auth/logout', methods=['POST'])
@login_required
def api_logout():
    """API: 用户登出"""
    logout_user()
    return api_response(200, '登出成功')

@app.route('/api/v1/auth/profile')
@login_required
def api_profile():
    """API: 获取当前用户信息"""
    return api_response(200, 'success', current_user.to_dict())

@app.route('/api/v1/products')
def api_products():
    """API: 获取商品列表"""
    page = request.args.get('page', 1, type=int)
    size = request.args.get('size', 20, type=int)
    category_id = request.args.get('category_id', type=int)
    keyword = request.args.get('keyword', '')

    query = Product.query.filter_by(is_active=True)

    if category_id:
        query = query.filter_by(category_id=category_id)

    if keyword:
        query = query.filter(Product.name.contains(keyword))

    pagination = query.paginate(page=page, per_page=size, error_out=False)

    return api_response(200, 'success', {
        'products': [p.to_dict() for p in pagination.items],
        'pagination': {
            'page': page,
            'size': size,
            'total': pagination.total,
            'pages': pagination.pages
        }
    })

@app.route('/api/v1/products/<int:product_id>')
def api_product_detail(product_id):
    """API: 获取商品详情"""
    product = Product.query.get(product_id)
    if not product or not product.is_active:
        return api_response(2001, '商品不存在')

    # 增加浏览次数
    product.view_count += 1
    db.session.commit()

    return api_response(200, 'success', product.to_dict())

@app.route('/api/v1/categories')
def api_categories():
    """API: 获取商品分类"""
    parent_id = request.args.get('parent_id', type=int)

    if parent_id is not None:
        categories = Category.query.filter_by(parent_id=parent_id, is_active=True).all()
    else:
        categories = Category.query.filter_by(parent_id=None, is_active=True).all()

    return api_response(200, 'success', [c.to_dict() for c in categories])

@app.route('/api/v1/cart')
@login_required
def api_cart():
    """API: 获取购物车"""
    cart_items = CartItem.query.filter_by(user_id=current_user.id).all()
    total_amount = sum(item.subtotal for item in cart_items)

    return api_response(200, 'success', {
        'items': [item.to_dict() for item in cart_items],
        'total_quantity': sum(item.quantity for item in cart_items),
        'total_amount': total_amount
    })

@app.route('/api/v1/cart/items', methods=['POST'])
@login_required
def api_add_to_cart():
    """API: 添加商品到购物车"""
    data = request.get_json()

    product_id = data.get('product_id')
    quantity = data.get('quantity', 1)

    if not product_id or quantity <= 0:
        return api_response(400, '参数错误')

    # 检查商品是否存在
    product = Product.query.get(product_id)
    if not product or not product.is_active:
        return api_response(2001, '商品不存在')

    # 检查库存
    if product.stock < quantity:
        return api_response(2002, '商品库存不足')

    # 查找是否已存在购物车项
    cart_item = CartItem.query.filter_by(user_id=current_user.id, product_id=product_id).first()

    if cart_item:
        # 更新数量
        new_quantity = cart_item.quantity + quantity
        if product.stock < new_quantity:
            return api_response(2002, '商品库存不足')
        cart_item.quantity = new_quantity
    else:
        # 创建新的购物车项
        cart_item = CartItem(user_id=current_user.id, product_id=product_id, quantity=quantity)
        db.session.add(cart_item)

    db.session.commit()

    # 计算购物车总数量
    total_quantity = db.session.query(db.func.sum(CartItem.quantity))\
                              .filter_by(user_id=current_user.id).scalar() or 0

    return api_response(200, '添加成功', {
        'cart_item_id': cart_item.id,
        'total_quantity': total_quantity
    })

@app.route('/api/v1/cart/items/<int:item_id>', methods=['PUT'])
@login_required
def api_update_cart_item(item_id):
    """API: 更新购物车商品数量"""
    data = request.get_json()
    quantity = data.get('quantity')

    if not quantity or quantity <= 0:
        return api_response(400, '数量必须大于0')

    cart_item = CartItem.query.filter_by(id=item_id, user_id=current_user.id).first()
    if not cart_item:
        return api_response(404, '购物车项不存在')

    # 检查库存
    if cart_item.product.stock < quantity:
        return api_response(2002, '商品库存不足')

    cart_item.quantity = quantity
    db.session.commit()

    # 计算购物车总金额
    cart_items = CartItem.query.filter_by(user_id=current_user.id).all()
    total_amount = sum(item.subtotal for item in cart_items)

    return api_response(200, '更新成功', {
        'subtotal': cart_item.subtotal,
        'total_amount': total_amount
    })

@app.route('/api/v1/cart/items/<int:item_id>', methods=['DELETE'])
@login_required
def api_delete_cart_item(item_id):
    """API: 删除购物车商品"""
    cart_item = CartItem.query.filter_by(id=item_id, user_id=current_user.id).first()
    if not cart_item:
        return api_response(404, '购物车项不存在')

    db.session.delete(cart_item)
    db.session.commit()

    # 计算购物车总数量和金额
    cart_items = CartItem.query.filter_by(user_id=current_user.id).all()
    total_quantity = sum(item.quantity for item in cart_items)
    total_amount = sum(item.subtotal for item in cart_items)

    return api_response(200, '删除成功', {
        'total_quantity': total_quantity,
        'total_amount': total_amount
    })

@app.route('/api/v1/orders', methods=['POST'])
@login_required
def api_create_order():
    """API: 创建订单"""
    data = request.get_json()
    address_id = data.get('address_id')
    remark = data.get('remark', '')

    if not address_id:
        return api_response(400, '请选择收货地址')

    # 获取用户地址
    address = UserAddress.query.filter_by(id=address_id, user_id=current_user.id).first()
    if not address:
        return api_response(4001, '地址不存在')

    # 获取购物车商品
    cart_items = CartItem.query.filter_by(user_id=current_user.id).all()
    if not cart_items:
        return api_response(3001, '购物车为空')

    # 检查库存并计算总金额
    total_amount = 0
    order_items_data = []

    for cart_item in cart_items:
        product = cart_item.product
        if product.stock < cart_item.quantity:
            return api_response(2002, f'商品"{product.name}"库存不足')

        item_total = float(product.price * cart_item.quantity)
        total_amount += item_total

        order_items_data.append({
            'product_id': product.id,
            'product_name': product.name,
            'product_image': product.image_url,
            'price': product.price,
            'quantity': cart_item.quantity,
            'total_amount': item_total
        })

    # 创建订单
    order = Order(
        order_no=generate_order_no(),
        user_id=current_user.id,
        total_amount=total_amount,
        receiver_name=address.receiver_name,
        receiver_phone=address.receiver_phone,
        receiver_address=f"{address.province} {address.city} {address.district} {address.detail_address}",
        remark=remark
    )
    db.session.add(order)
    db.session.flush()  # 获取订单ID

    # 创建订单项并扣减库存
    for item_data in order_items_data:
        order_item = OrderItem(
            order_id=order.id,
            **item_data
        )
        db.session.add(order_item)

        # 扣减库存
        product = Product.query.get(item_data['product_id'])
        product.stock -= item_data['quantity']
        product.sales_count += item_data['quantity']

    # 清空购物车
    for cart_item in cart_items:
        db.session.delete(cart_item)

    db.session.commit()

    return api_response(200, '订单创建成功', {
        'order_id': order.id,
        'order_no': order.order_no,
        'total_amount': float(order.total_amount),
        'status': order.status
    })

@app.route('/api/v1/orders')
@login_required
def api_orders():
    """API: 获取订单列表"""
    page = request.args.get('page', 1, type=int)
    size = request.args.get('size', 10, type=int)
    status = request.args.get('status', type=int)

    query = Order.query.filter_by(user_id=current_user.id)

    if status:
        query = query.filter_by(status=status)

    pagination = query.order_by(Order.created_at.desc())\
                      .paginate(page=page, per_page=size, error_out=False)

    return api_response(200, 'success', {
        'orders': [order.to_dict() for order in pagination.items],
        'pagination': {
            'page': page,
            'size': size,
            'total': pagination.total,
            'pages': pagination.pages
        }
    })

@app.route('/api/v1/orders/<int:order_id>')
@login_required
def api_order_detail(order_id):
    """API: 获取订单详情"""
    order = Order.query.filter_by(id=order_id, user_id=current_user.id).first()
    if not order:
        return api_response(3002, '订单不存在')

    order_data = order.to_dict()
    order_data['items'] = [item.to_dict() for item in order.items]

    return api_response(200, 'success', order_data)

@app.route('/api/v1/addresses')
@login_required
def api_addresses():
    """API: 获取地址列表"""
    addresses = UserAddress.query.filter_by(user_id=current_user.id).all()
    return api_response(200, 'success', [addr.to_dict() for addr in addresses])

@app.route('/api/v1/addresses', methods=['POST'])
@login_required
def api_add_address():
    """API: 添加地址"""
    data = request.get_json()

    required_fields = ['receiver_name', 'receiver_phone', 'province', 'city', 'district', 'detail_address']
    if not all(data.get(field) for field in required_fields):
        return api_response(400, '请填写完整地址信息')

    # 如果设置为默认地址，先取消其他默认地址
    if data.get('is_default'):
        UserAddress.query.filter_by(user_id=current_user.id, is_default=True)\
                         .update({'is_default': False})

    address = UserAddress(
        user_id=current_user.id,
        receiver_name=data['receiver_name'],
        receiver_phone=data['receiver_phone'],
        province=data['province'],
        city=data['city'],
        district=data['district'],
        detail_address=data['detail_address'],
        is_default=data.get('is_default', False)
    )

    db.session.add(address)
    db.session.commit()

    return api_response(200, '添加成功', {'address_id': address.id})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
