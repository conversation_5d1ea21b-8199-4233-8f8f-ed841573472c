# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: Python application

on:
  push:
    branches: [ "master" ]
  pull_request:
    branches: [ "master" ]

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
    - name: Shutdown Ubuntu MySQL (SUDO)
      run: sudo service mysql stop
    - uses: actions/checkout@v3
    - name: Set up Python 3.10
      uses: actions/setup-python@v3
      with:
        python-version: "3.10"
    - uses: mirromutth/mysql-action@v1.1
      with:
        mysql database: 'testshop'
        mysql root password: 'root'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install flake8 pytest cryptography
        pip install -r requirements.txt
    - name: Lint with flake8
      run: |
        # stop the build if there are Python syntax errors or undefined names
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
        flake8 . --count --exit-zero --max-complexity=15 --max-line-length=127 --statistics
    - name: Test with pytest
      run: |
        pytest
