# 项目一：简易在线商城系统 - 数据库设计文档

## 1. 数据库概述

### 1.1 设计目标
- 支持电商系统核心业务功能
- 保证数据一致性和完整性
- 优化查询性能
- 支持系统扩展需求

### 1.2 设计原则
- **规范化设计**：减少数据冗余，保证数据一致性
- **性能优化**：合理设计索引，优化查询效率
- **扩展性**：预留扩展字段，支持功能升级
- **安全性**：敏感数据加密存储

### 1.3 技术规范
- **数据库**：MySQL 8.0
- **字符集**：utf8mb4
- **存储引擎**：InnoDB
- **命名规范**：下划线命名法

## 2. 数据库E-R图

```
┌─────────────┐       ┌─────────────┐       ┌─────────────┐
│    Users    │       │  Categories │       │  Products   │
│             │       │             │       │             │
│ id (PK)     │       │ id (PK)     │       │ id (PK)     │
│ username    │       │ name        │       │ name        │
│ email       │       │ description │       │ description │
│ password    │       │ parent_id   │       │ price       │
│ created_at  │       │ sort_order  │       │ stock       │
│ updated_at  │       │ created_at  │       │ category_id │
│ is_active   │       └─────────────┘       │ image_url   │
└─────────────┘              │              │ is_active   │
       │                     │              │ created_at  │
       │                     │              │ updated_at  │
       │                     └──────────────┤             │
       │                                    └─────────────┘
       │                                           │
       │                                           │
       ▼                                           ▼
┌─────────────┐                            ┌─────────────┐
│ CartItems   │                            │ OrderItems  │
│             │                            │             │
│ id (PK)     │                            │ id (PK)     │
│ user_id (FK)│                            │ order_id(FK)│
│ product_id  │                            │ product_id  │
│ quantity    │                            │ quantity    │
│ created_at  │                            │ price       │
│ updated_at  │                            │ created_at  │
└─────────────┘                            └─────────────┘
       │                                           ▲
       │                                           │
       │                                           │
       │                                    ┌─────────────┐
       │                                    │   Orders    │
       │                                    │             │
       │                                    │ id (PK)     │
       │                                    │ user_id (FK)│
       │                                    │ total_amount│
       │                                    │ status      │
       │                                    │ shipping_   │
       │                                    │ address     │
       │                                    │ created_at  │
       │                                    │ updated_at  │
       │                                    └─────────────┘
       │                                           ▲
       │                                           │
       └───────────────────────────────────────────┘
```

## 3. 数据表设计

### 3.1 用户相关表

#### 3.1.1 用户表 (users)
**表描述**：存储用户基本信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键，用户ID |
| username | VARCHAR | 50 | NOT NULL | - | 用户名，唯一 |
| email | VARCHAR | 100 | NOT NULL | - | 邮箱，唯一 |
| password_hash | VARCHAR | 255 | NOT NULL | - | 密码哈希值 |
| nickname | VARCHAR | 50 | NULL | NULL | 昵称 |
| avatar_url | VARCHAR | 255 | NULL | NULL | 头像URL |
| phone | VARCHAR | 20 | NULL | NULL | 手机号 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否激活 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- UNIQUE KEY uk_username (username)
- UNIQUE KEY uk_email (email)
- KEY idx_created_at (created_at)

#### 3.1.2 用户地址表 (user_addresses)
**表描述**：存储用户收货地址信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| user_id | INT | - | NOT NULL | - | 用户ID，外键 |
| receiver_name | VARCHAR | 50 | NOT NULL | - | 收货人姓名 |
| receiver_phone | VARCHAR | 20 | NOT NULL | - | 收货人电话 |
| province | VARCHAR | 50 | NOT NULL | - | 省份 |
| city | VARCHAR | 50 | NOT NULL | - | 城市 |
| district | VARCHAR | 50 | NOT NULL | - | 区县 |
| detail_address | VARCHAR | 200 | NOT NULL | - | 详细地址 |
| is_default | BOOLEAN | - | NOT NULL | FALSE | 是否默认地址 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- KEY idx_user_id (user_id)
- FOREIGN KEY fk_address_user (user_id) REFERENCES users(id)

### 3.2 商品相关表

#### 3.2.1 商品分类表 (categories)
**表描述**：存储商品分类信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键，分类ID |
| name | VARCHAR | 100 | NOT NULL | - | 分类名称 |
| description | TEXT | - | NULL | NULL | 分类描述 |
| parent_id | INT | - | NULL | NULL | 父分类ID |
| sort_order | INT | - | NOT NULL | 0 | 排序顺序 |
| icon_url | VARCHAR | 255 | NULL | NULL | 分类图标URL |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- KEY idx_parent_id (parent_id)
- KEY idx_sort_order (sort_order)

#### 3.2.2 商品表 (products)
**表描述**：存储商品基本信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键，商品ID |
| name | VARCHAR | 200 | NOT NULL | - | 商品名称 |
| description | TEXT | - | NULL | NULL | 商品描述 |
| price | DECIMAL | 10,2 | NOT NULL | - | 商品价格 |
| original_price | DECIMAL | 10,2 | NULL | NULL | 原价 |
| stock | INT | - | NOT NULL | 0 | 库存数量 |
| category_id | INT | - | NOT NULL | - | 分类ID，外键 |
| image_url | VARCHAR | 255 | NULL | NULL | 主图URL |
| images | JSON | - | NULL | NULL | 商品图片列表 |
| sku | VARCHAR | 100 | NULL | NULL | 商品SKU |
| weight | DECIMAL | 8,3 | NULL | NULL | 商品重量(kg) |
| sales_count | INT | - | NOT NULL | 0 | 销售数量 |
| view_count | INT | - | NOT NULL | 0 | 浏览次数 |
| is_featured | BOOLEAN | - | NOT NULL | FALSE | 是否推荐 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否上架 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- KEY idx_category_id (category_id)
- KEY idx_price (price)
- KEY idx_sales_count (sales_count)
- KEY idx_is_featured (is_featured)
- KEY idx_is_active (is_active)
- FOREIGN KEY fk_product_category (category_id) REFERENCES categories(id)

### 3.3 购物车相关表

#### 3.3.1 购物车表 (cart_items)
**表描述**：存储用户购物车商品信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| user_id | INT | - | NOT NULL | - | 用户ID，外键 |
| product_id | INT | - | NOT NULL | - | 商品ID，外键 |
| quantity | INT | - | NOT NULL | 1 | 商品数量 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- UNIQUE KEY uk_user_product (user_id, product_id)
- KEY idx_user_id (user_id)
- KEY idx_product_id (product_id)
- FOREIGN KEY fk_cart_user (user_id) REFERENCES users(id)
- FOREIGN KEY fk_cart_product (product_id) REFERENCES products(id)

### 3.4 订单相关表

#### 3.4.1 订单表 (orders)
**表描述**：存储订单基本信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键，订单ID |
| order_no | VARCHAR | 32 | NOT NULL | - | 订单编号，唯一 |
| user_id | INT | - | NOT NULL | - | 用户ID，外键 |
| total_amount | DECIMAL | 10,2 | NOT NULL | - | 订单总金额 |
| product_amount | DECIMAL | 10,2 | NOT NULL | - | 商品金额 |
| shipping_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 运费 |
| discount_amount | DECIMAL | 10,2 | NOT NULL | 0.00 | 优惠金额 |
| status | TINYINT | - | NOT NULL | 1 | 订单状态 |
| payment_status | TINYINT | - | NOT NULL | 0 | 支付状态 |
| shipping_status | TINYINT | - | NOT NULL | 0 | 发货状态 |
| receiver_name | VARCHAR | 50 | NOT NULL | - | 收货人姓名 |
| receiver_phone | VARCHAR | 20 | NOT NULL | - | 收货人电话 |
| receiver_address | VARCHAR | 500 | NOT NULL | - | 收货地址 |
| remark | VARCHAR | 500 | NULL | NULL | 订单备注 |
| paid_at | DATETIME | - | NULL | NULL | 支付时间 |
| shipped_at | DATETIME | - | NULL | NULL | 发货时间 |
| completed_at | DATETIME | - | NULL | NULL | 完成时间 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- UNIQUE KEY uk_order_no (order_no)
- KEY idx_user_id (user_id)
- KEY idx_status (status)
- KEY idx_created_at (created_at)
- FOREIGN KEY fk_order_user (user_id) REFERENCES users(id)

#### 3.4.2 订单商品表 (order_items)
**表描述**：存储订单中的商品详情

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| order_id | INT | - | NOT NULL | - | 订单ID，外键 |
| product_id | INT | - | NOT NULL | - | 商品ID，外键 |
| product_name | VARCHAR | 200 | NOT NULL | - | 商品名称快照 |
| product_image | VARCHAR | 255 | NULL | NULL | 商品图片快照 |
| product_sku | VARCHAR | 100 | NULL | NULL | 商品SKU快照 |
| price | DECIMAL | 10,2 | NOT NULL | - | 商品单价 |
| quantity | INT | - | NOT NULL | - | 购买数量 |
| total_amount | DECIMAL | 10,2 | NOT NULL | - | 小计金额 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |

**索引设计**：
- PRIMARY KEY (id)
- KEY idx_order_id (order_id)
- KEY idx_product_id (product_id)
- FOREIGN KEY fk_item_order (order_id) REFERENCES orders(id)
- FOREIGN KEY fk_item_product (product_id) REFERENCES products(id)

### 3.5 管理员相关表

#### 3.5.1 管理员表 (admins)
**表描述**：存储管理员账户信息

| 字段名 | 数据类型 | 长度 | 是否为空 | 默认值 | 说明 |
|--------|----------|------|----------|--------|------|
| id | INT | - | NOT NULL | AUTO_INCREMENT | 主键 |
| username | VARCHAR | 50 | NOT NULL | - | 管理员用户名 |
| email | VARCHAR | 100 | NOT NULL | - | 邮箱 |
| password_hash | VARCHAR | 255 | NOT NULL | - | 密码哈希值 |
| real_name | VARCHAR | 50 | NULL | NULL | 真实姓名 |
| role | VARCHAR | 20 | NOT NULL | 'admin' | 角色 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| last_login_at | DATETIME | - | NULL | NULL | 最后登录时间 |
| created_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | CURRENT_TIMESTAMP ON UPDATE | 更新时间 |

**索引设计**：
- PRIMARY KEY (id)
- UNIQUE KEY uk_admin_username (username)
- UNIQUE KEY uk_admin_email (email)

## 4. 数据字典

### 4.1 订单状态枚举
| 值 | 状态名称 | 说明 |
|----|----------|------|
| 1 | 待支付 | 订单已创建，等待支付 |
| 2 | 待发货 | 已支付，等待发货 |
| 3 | 已发货 | 已发货，等待收货 |
| 4 | 已完成 | 订单完成 |
| 5 | 已取消 | 订单已取消 |
| 6 | 已退款 | 订单已退款 |

### 4.2 支付状态枚举
| 值 | 状态名称 | 说明 |
|----|----------|------|
| 0 | 未支付 | 等待支付 |
| 1 | 已支付 | 支付成功 |
| 2 | 支付失败 | 支付失败 |
| 3 | 已退款 | 已退款 |

### 4.3 发货状态枚举
| 值 | 状态名称 | 说明 |
|----|----------|------|
| 0 | 未发货 | 等待发货 |
| 1 | 已发货 | 已发货 |
| 2 | 已收货 | 用户确认收货 |

## 5. 数据库优化策略

### 5.1 索引优化
- **主键索引**：所有表都有自增主键
- **唯一索引**：用户名、邮箱、订单号等唯一字段
- **普通索引**：外键字段、查询频繁字段
- **复合索引**：多字段组合查询

### 5.2 查询优化
- **分页查询**：使用LIMIT进行分页
- **避免全表扫描**：合理使用WHERE条件
- **连接查询优化**：适当使用JOIN
- **子查询优化**：避免复杂子查询

### 5.3 存储优化
- **数据类型选择**：选择合适的数据类型
- **字段长度**：根据实际需求设置字段长度
- **NULL值处理**：合理使用NULL和默认值
- **JSON字段**：复杂数据使用JSON存储

## 6. 数据安全

### 6.1 敏感数据保护
- **密码加密**：使用bcrypt加密存储
- **数据脱敏**：日志中隐藏敏感信息
- **访问控制**：数据库用户权限控制

### 6.2 数据备份
- **定期备份**：每日自动备份
- **增量备份**：减少备份时间和存储空间
- **备份验证**：定期验证备份文件完整性

### 6.3 数据恢复
- **恢复策略**：制定数据恢复计划
- **测试恢复**：定期测试恢复流程
- **灾难恢复**：异地备份和恢复方案
