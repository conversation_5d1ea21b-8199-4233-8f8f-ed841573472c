# 项目一：简易在线商城系统 - API接口文档

## 1. 接口概述

### 1.1 接口规范
- **协议**：HTTP/HTTPS
- **数据格式**：JSON
- **字符编码**：UTF-8
- **API版本**：v1
- **基础URL**：`http://localhost:5000/api/v1`

### 1.2 通用响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-01-01T12:00:00Z"
}
```

### 1.3 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 1.4 认证方式
- **Session认证**：基于Cookie的会话认证
- **请求头**：`Cookie: session=xxx`

## 2. 用户认证接口

### 2.1 用户注册
**接口地址**：`POST /api/v1/auth/register`

**请求参数**：
```json
{
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "confirm_password": "password123"
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名，3-20字符 |
| email | string | 是 | 邮箱地址 |
| password | string | 是 | 密码，6-20字符 |
| confirm_password | string | 是 | 确认密码 |

**响应示例**：
```json
{
    "code": 200,
    "message": "注册成功",
    "data": {
        "user_id": 1,
        "username": "testuser",
        "email": "<EMAIL>"
    }
}
```

### 2.2 用户登录
**接口地址**：`POST /api/v1/auth/login`

**请求参数**：
```json
{
    "username": "testuser",
    "password": "password123",
    "remember_me": false
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| username | string | 是 | 用户名或邮箱 |
| password | string | 是 | 密码 |
| remember_me | boolean | 否 | 是否记住登录状态 |

**响应示例**：
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "user_id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "session_id": "abc123"
    }
}
```

### 2.3 用户登出
**接口地址**：`POST /api/v1/auth/logout`

**请求参数**：无

**响应示例**：
```json
{
    "code": 200,
    "message": "登出成功",
    "data": null
}
```

### 2.4 获取当前用户信息
**接口地址**：`GET /api/v1/auth/profile`

**请求参数**：无

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "user_id": 1,
        "username": "testuser",
        "email": "<EMAIL>",
        "nickname": "测试用户",
        "avatar_url": "http://example.com/avatar.jpg",
        "phone": "13800138000",
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

## 3. 商品接口

### 3.1 获取商品列表
**接口地址**：`GET /api/v1/products`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认20 |
| category_id | int | 否 | 分类ID |
| keyword | string | 否 | 搜索关键词 |
| sort | string | 否 | 排序方式：price_asc, price_desc, sales_desc |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "products": [
            {
                "id": 1,
                "name": "iPhone 15",
                "description": "最新款iPhone",
                "price": 5999.00,
                "original_price": 6999.00,
                "stock": 100,
                "category_id": 1,
                "image_url": "http://example.com/iphone15.jpg",
                "sales_count": 1000,
                "is_featured": true
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 100,
            "pages": 5
        }
    }
}
```

### 3.2 获取商品详情
**接口地址**：`GET /api/v1/products/{product_id}`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | int | 是 | 商品ID |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "name": "iPhone 15",
        "description": "最新款iPhone，配备A17芯片...",
        "price": 5999.00,
        "original_price": 6999.00,
        "stock": 100,
        "category": {
            "id": 1,
            "name": "手机数码"
        },
        "images": [
            "http://example.com/iphone15_1.jpg",
            "http://example.com/iphone15_2.jpg"
        ],
        "sales_count": 1000,
        "view_count": 5000,
        "is_featured": true,
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

### 3.3 获取商品分类
**接口地址**：`GET /api/v1/categories`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| parent_id | int | 否 | 父分类ID，不传则获取顶级分类 |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "手机数码",
            "description": "手机、平板、数码产品",
            "parent_id": null,
            "icon_url": "http://example.com/category_phone.png",
            "product_count": 150,
            "children": [
                {
                    "id": 11,
                    "name": "智能手机",
                    "product_count": 100
                }
            ]
        }
    ]
}
```

### 3.4 搜索商品
**接口地址**：`GET /api/v1/products/search`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| q | string | 是 | 搜索关键词 |
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认20 |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "keyword": "iPhone",
        "products": [
            {
                "id": 1,
                "name": "iPhone 15",
                "price": 5999.00,
                "image_url": "http://example.com/iphone15.jpg"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 20,
            "total": 10,
            "pages": 1
        }
    }
}
```

## 4. 购物车接口

### 4.1 获取购物车
**接口地址**：`GET /api/v1/cart`

**请求参数**：无

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "id": 1,
                "product": {
                    "id": 1,
                    "name": "iPhone 15",
                    "price": 5999.00,
                    "image_url": "http://example.com/iphone15.jpg",
                    "stock": 100
                },
                "quantity": 2,
                "subtotal": 11998.00
            }
        ],
        "total_quantity": 2,
        "total_amount": 11998.00
    }
}
```

### 4.2 添加商品到购物车
**接口地址**：`POST /api/v1/cart/items`

**请求参数**：
```json
{
    "product_id": 1,
    "quantity": 2
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| product_id | int | 是 | 商品ID |
| quantity | int | 是 | 数量，大于0 |

**响应示例**：
```json
{
    "code": 200,
    "message": "添加成功",
    "data": {
        "cart_item_id": 1,
        "total_quantity": 2
    }
}
```

### 4.3 更新购物车商品数量
**接口地址**：`PUT /api/v1/cart/items/{item_id}`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| item_id | int | 是 | 购物车项ID |

**请求参数**：
```json
{
    "quantity": 3
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "subtotal": 17997.00,
        "total_amount": 17997.00
    }
}
```

### 4.4 删除购物车商品
**接口地址**：`DELETE /api/v1/cart/items/{item_id}`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| item_id | int | 是 | 购物车项ID |

**响应示例**：
```json
{
    "code": 200,
    "message": "删除成功",
    "data": {
        "total_quantity": 0,
        "total_amount": 0.00
    }
}
```

## 5. 订单接口

### 5.1 创建订单
**接口地址**：`POST /api/v1/orders`

**请求参数**：
```json
{
    "address_id": 1,
    "remark": "请尽快发货"
}
```

**参数说明**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| address_id | int | 是 | 收货地址ID |
| remark | string | 否 | 订单备注 |

**响应示例**：
```json
{
    "code": 200,
    "message": "订单创建成功",
    "data": {
        "order_id": 1,
        "order_no": "ORD20240101120000001",
        "total_amount": 11998.00,
        "status": 1
    }
}
```

### 5.2 获取订单列表
**接口地址**：`GET /api/v1/orders`

**请求参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | int | 否 | 页码，默认1 |
| size | int | 否 | 每页数量，默认10 |
| status | int | 否 | 订单状态筛选 |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "orders": [
            {
                "id": 1,
                "order_no": "ORD20240101120000001",
                "total_amount": 11998.00,
                "status": 1,
                "status_text": "待支付",
                "item_count": 2,
                "created_at": "2024-01-01T12:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "size": 10,
            "total": 5,
            "pages": 1
        }
    }
}
```

### 5.3 获取订单详情
**接口地址**：`GET /api/v1/orders/{order_id}`

**路径参数**：
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| order_id | int | 是 | 订单ID |

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "order_no": "ORD20240101120000001",
        "total_amount": 11998.00,
        "product_amount": 11998.00,
        "shipping_amount": 0.00,
        "status": 1,
        "status_text": "待支付",
        "receiver_name": "张三",
        "receiver_phone": "13800138000",
        "receiver_address": "北京市朝阳区xxx街道xxx号",
        "remark": "请尽快发货",
        "items": [
            {
                "id": 1,
                "product_name": "iPhone 15",
                "product_image": "http://example.com/iphone15.jpg",
                "price": 5999.00,
                "quantity": 2,
                "total_amount": 11998.00
            }
        ],
        "created_at": "2024-01-01T12:00:00Z"
    }
}
```

## 6. 用户地址接口

### 6.1 获取地址列表
**接口地址**：`GET /api/v1/addresses`

**响应示例**：
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "receiver_name": "张三",
            "receiver_phone": "13800138000",
            "province": "北京市",
            "city": "北京市",
            "district": "朝阳区",
            "detail_address": "xxx街道xxx号",
            "is_default": true
        }
    ]
}
```

### 6.2 添加地址
**接口地址**：`POST /api/v1/addresses`

**请求参数**：
```json
{
    "receiver_name": "张三",
    "receiver_phone": "13800138000",
    "province": "北京市",
    "city": "北京市",
    "district": "朝阳区",
    "detail_address": "xxx街道xxx号",
    "is_default": false
}
```

**响应示例**：
```json
{
    "code": 200,
    "message": "添加成功",
    "data": {
        "address_id": 1
    }
}
```

## 7. 错误码说明

| 错误码 | 错误信息 | 说明 |
|--------|----------|------|
| 1001 | 用户名已存在 | 注册时用户名重复 |
| 1002 | 邮箱已存在 | 注册时邮箱重复 |
| 1003 | 用户名或密码错误 | 登录失败 |
| 1004 | 用户未登录 | 需要登录的接口未登录 |
| 2001 | 商品不存在 | 商品ID无效 |
| 2002 | 商品库存不足 | 购买数量超过库存 |
| 3001 | 购物车为空 | 创建订单时购物车为空 |
| 3002 | 订单不存在 | 订单ID无效 |
| 4001 | 地址不存在 | 地址ID无效 |
