# 项目一：简易在线商城系统 - 系统架构设计文档

## 1. 架构概述

### 1.1 架构目标
- 构建可扩展、可维护的电商系统架构
- 采用成熟稳定的技术栈
- 支持快速开发和部署
- 为后续功能扩展预留空间

### 1.2 架构原则
- **分层架构**：清晰的分层结构，职责分离
- **模块化设计**：功能模块独立，低耦合高内聚
- **可扩展性**：支持水平和垂直扩展
- **安全性**：数据安全和访问控制
- **可维护性**：代码结构清晰，便于维护

## 2. 技术选型

### 2.1 后端技术栈

#### 2.1.1 Web框架
**选择：Flask 3.0**
- **优势**：轻量级、灵活、学习成本低
- **适用场景**：中小型Web应用
- **扩展性**：丰富的扩展插件生态

#### 2.1.2 数据库
**选择：MySQL 8.0**
- **优势**：成熟稳定、性能优秀、社区活跃
- **适用场景**：关系型数据存储
- **特性**：ACID事务、索引优化、主从复制

#### 2.1.3 ORM框架
**选择：SQLAlchemy 2.0**
- **优势**：功能强大、灵活性高
- **特性**：数据库迁移、关系映射、查询优化

#### 2.1.4 身份认证
**选择：Flask-Login**
- **功能**：用户会话管理、登录状态维护
- **安全性**：密码加密、会话保护

### 2.2 前端技术栈

#### 2.2.1 基础技术
- **HTML5**：语义化标签、表单验证
- **CSS3**：样式设计、响应式布局
- **JavaScript ES6+**：交互逻辑、异步请求

#### 2.2.2 UI框架
**选择：Bootstrap 5**
- **优势**：响应式设计、组件丰富
- **特性**：栅格系统、预设样式、JavaScript插件

#### 2.2.3 构建工具
**选择：Webpack 5**
- **功能**：资源打包、代码压缩、热更新
- **优化**：代码分割、缓存策略

### 2.3 开发工具

#### 2.3.1 版本控制
- **Git**：代码版本管理
- **GitHub**：代码托管、协作开发

#### 2.3.2 开发环境
- **Python 3.12**：后端开发语言
- **Node.js**：前端构建环境
- **Docker**：容器化部署

## 3. 系统架构设计

### 3.1 整体架构图

```
┌─────────────────────────────────────────────────────────┐
│                    用户界面层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   前台页面   │  │   后台管理   │  │   移动端     │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                    Web服务层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   路由控制   │  │   视图处理   │  │   API接口    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                    业务逻辑层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   用户管理   │  │   商品管理   │  │   订单管理    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                    数据访问层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   ORM映射    │  │   数据验证   │  │   缓存管理    │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────┐
│                    数据存储层                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐      │
│  │   MySQL     │  │   Redis     │  │   文件存储    │      │
│  │   数据库     │  │   缓存      │  │   (图片)     │      │
│  └─────────────┘  └─────────────┘  └─────────────┘      │
└─────────────────────────────────────────────────────────┘
```

### 3.2 模块划分

#### 3.2.1 核心模块
1. **用户模块（account）**
   - 用户注册、登录、认证
   - 个人信息管理
   - 权限控制

2. **商品模块（product）**
   - 商品信息管理
   - 分类管理
   - 库存管理

3. **购物车模块（cart）**
   - 购物车操作
   - 商品数量管理
   - 价格计算

4. **订单模块（order）**
   - 订单创建和管理
   - 订单状态跟踪
   - 订单历史查询

5. **后台管理模块（admin）**
   - 管理员认证
   - 商品管理界面
   - 订单管理界面

#### 3.2.2 公共模块
1. **数据库模块（database）**
   - 数据库连接管理
   - ORM模型定义
   - 数据迁移

2. **工具模块（utils）**
   - 通用工具函数
   - 数据验证
   - 异常处理

3. **配置模块（config）**
   - 环境配置
   - 数据库配置
   - 安全配置

## 4. 数据库设计

### 4.1 数据库架构
- **主数据库**：MySQL 8.0
- **连接池**：SQLAlchemy连接池管理
- **事务管理**：自动事务提交和回滚

### 4.2 核心数据表

#### 4.2.1 用户相关表
```sql
-- 用户表
users (
    id, username, email, password_hash, 
    created_at, updated_at, is_active
)

-- 用户地址表
user_addresses (
    id, user_id, name, phone, address, 
    is_default, created_at
)
```

#### 4.2.2 商品相关表
```sql
-- 商品分类表
categories (
    id, name, description, parent_id, 
    sort_order, created_at
)

-- 商品表
products (
    id, name, description, price, stock, 
    category_id, image_url, is_active, 
    created_at, updated_at
)
```

#### 4.2.3 订单相关表
```sql
-- 购物车表
cart_items (
    id, user_id, product_id, quantity, 
    created_at, updated_at
)

-- 订单表
orders (
    id, user_id, total_amount, status, 
    shipping_address, created_at, updated_at
)

-- 订单商品表
order_items (
    id, order_id, product_id, quantity, 
    price, created_at
)
```

## 5. API设计

### 5.1 API架构
- **RESTful设计**：遵循REST架构风格
- **统一响应格式**：标准化JSON响应
- **版本控制**：URL路径版本控制
- **错误处理**：统一错误码和消息

### 5.2 API分组

#### 5.2.1 用户API
```
POST /api/v1/auth/register    # 用户注册
POST /api/v1/auth/login       # 用户登录
POST /api/v1/auth/logout      # 用户登出
GET  /api/v1/user/profile     # 获取用户信息
PUT  /api/v1/user/profile     # 更新用户信息
```

#### 5.2.2 商品API
```
GET  /api/v1/products         # 获取商品列表
GET  /api/v1/products/{id}    # 获取商品详情
GET  /api/v1/categories       # 获取分类列表
GET  /api/v1/search           # 搜索商品
```

#### 5.2.3 购物车API
```
GET    /api/v1/cart           # 获取购物车
POST   /api/v1/cart/items     # 添加商品到购物车
PUT    /api/v1/cart/items/{id} # 更新购物车商品
DELETE /api/v1/cart/items/{id} # 删除购物车商品
```

#### 5.2.4 订单API
```
POST /api/v1/orders           # 创建订单
GET  /api/v1/orders           # 获取订单列表
GET  /api/v1/orders/{id}      # 获取订单详情
PUT  /api/v1/orders/{id}      # 更新订单状态
```

## 6. 安全架构

### 6.1 身份认证
- **会话管理**：Flask-Login会话管理
- **密码安全**：bcrypt密码加密
- **登录保护**：防暴力破解机制

### 6.2 数据安全
- **SQL注入防护**：参数化查询
- **XSS防护**：输入输出过滤
- **CSRF防护**：CSRF令牌验证

### 6.3 访问控制
- **权限验证**：基于角色的访问控制
- **API鉴权**：请求头Token验证
- **敏感操作**：二次验证机制

## 7. 部署架构

### 7.1 开发环境
```
开发机器
├── Python 3.12 + Flask
├── MySQL 8.0
├── Node.js + Webpack
└── Git版本控制
```

### 7.2 生产环境
```
Linux服务器
├── Nginx (反向代理 + 静态文件)
├── Gunicorn (WSGI服务器)
├── Flask应用
├── MySQL数据库
└── 文件存储
```

### 7.3 容器化部署（可选）
```
Docker容器
├── Web容器 (Flask + Gunicorn)
├── 数据库容器 (MySQL)
├── 反向代理容器 (Nginx)
└── Docker Compose编排
```

## 8. 性能优化

### 8.1 数据库优化
- **索引优化**：关键字段建立索引
- **查询优化**：避免N+1查询问题
- **连接池**：数据库连接池管理

### 8.2 缓存策略
- **页面缓存**：静态页面缓存
- **数据缓存**：热点数据Redis缓存
- **浏览器缓存**：静态资源缓存策略

### 8.3 前端优化
- **资源压缩**：CSS/JS文件压缩
- **图片优化**：图片格式和大小优化
- **CDN加速**：静态资源CDN分发

## 9. 监控和日志

### 9.1 应用监控
- **性能监控**：响应时间、吞吐量
- **错误监控**：异常捕获和报告
- **资源监控**：CPU、内存使用率

### 9.2 日志管理
- **访问日志**：用户访问记录
- **错误日志**：系统错误记录
- **业务日志**：关键业务操作记录

## 10. 扩展性设计

### 10.1 水平扩展
- **负载均衡**：多实例部署
- **数据库分片**：数据水平分割
- **微服务化**：服务拆分和独立部署

### 10.2 功能扩展
- **插件机制**：功能模块插件化
- **API扩展**：版本兼容的API设计
- **第三方集成**：支付、物流等第三方服务
