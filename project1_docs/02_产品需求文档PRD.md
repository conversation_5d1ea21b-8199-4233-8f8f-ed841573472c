# 项目一：简易在线商城系统 - 产品需求文档（PRD）

## 1. 产品概述

### 1.1 产品定位
简易在线商城系统是一个面向中小型商户的电商平台，提供基础的商品展示、购物车、订单管理等核心电商功能。

### 1.2 产品目标
- 为用户提供简洁流畅的购物体验
- 为商户提供易用的商品和订单管理工具
- 构建可扩展的电商系统架构基础

### 1.3 目标用户画像
**普通消费者**
- 年龄：18-45岁
- 特征：熟悉网络购物，追求便捷体验
- 需求：快速找到商品，安全完成购买

**商城管理员**
- 角色：商户或运营人员
- 特征：需要管理商品和订单
- 需求：简单易用的管理界面

## 2. 功能详细规格

### 2.1 用户注册与登录模块

#### 2.1.1 用户注册
**功能描述**：新用户创建账户

**输入字段**：
- 用户名（必填，3-20字符，唯一）
- 邮箱（必填，格式验证，唯一）
- 密码（必填，6-20字符，包含字母和数字）
- 确认密码（必填，与密码一致）

**业务规则**：
- 用户名和邮箱不能重复
- 密码需要加密存储
- 注册成功后自动登录

**异常处理**：
- 用户名或邮箱已存在：提示用户更换
- 密码格式不正确：提示密码要求
- 网络异常：提示稍后重试

#### 2.1.2 用户登录
**功能描述**：已注册用户身份验证

**输入字段**：
- 用户名/邮箱（必填）
- 密码（必填）
- 记住我（可选）

**业务规则**：
- 支持用户名或邮箱登录
- 密码错误3次锁定账户30分钟
- 登录成功创建会话

### 2.2 商品展示模块

#### 2.2.1 首页展示
**功能描述**：展示商城首页内容

**展示内容**：
- 轮播图（3-5张推广图片）
- 推荐商品（8-12个热门商品）
- 商品分类导航
- 搜索框

**业务规则**：
- 推荐商品按销量或评分排序
- 分类显示商品数量
- 搜索支持商品名称模糊匹配

#### 2.2.2 商品列表页
**功能描述**：按分类展示商品列表

**展示内容**：
- 商品缩略图
- 商品名称
- 商品价格
- 商品评分（如有）

**功能特性**：
- 分页显示（每页20个商品）
- 价格排序（升序/降序）
- 分类筛选

#### 2.2.3 商品详情页
**功能描述**：展示单个商品的详细信息

**展示内容**：
- 商品图片（支持多图切换）
- 商品名称和价格
- 商品详细描述
- 库存状态
- 添加到购物车按钮

**业务规则**：
- 库存不足时禁用购买按钮
- 未登录用户点击购买跳转登录页

### 2.3 购物车模块

#### 2.3.1 添加商品到购物车
**功能描述**：将商品加入用户购物车

**输入参数**：
- 商品ID
- 购买数量（默认1）

**业务规则**：
- 检查库存是否充足
- 同一商品累加数量
- 实时更新购物车图标数量

#### 2.3.2 购物车管理
**功能描述**：查看和管理购物车商品

**展示内容**：
- 商品信息（图片、名称、价格）
- 购买数量（可修改）
- 小计金额
- 总计金额

**操作功能**：
- 修改商品数量
- 删除商品
- 清空购物车
- 结算下单

### 2.4 订单管理模块

#### 2.4.1 订单提交
**功能描述**：将购物车商品转换为订单

**输入信息**：
- 收货人姓名
- 收货地址
- 联系电话
- 订单备注（可选）

**业务流程**：
1. 验证购物车商品和库存
2. 计算订单总金额
3. 创建订单记录
4. 扣减商品库存
5. 清空购物车
6. 跳转到订单详情页

#### 2.4.2 订单查看
**功能描述**：用户查看自己的订单

**展示内容**：
- 订单编号和时间
- 订单状态
- 商品信息和数量
- 订单金额
- 收货信息

**订单状态**：
- 待处理：刚提交的订单
- 已确认：商家确认订单
- 已发货：商品已发出
- 已完成：用户确认收货
- 已取消：订单被取消

## 3. 后台管理功能规格

### 3.1 管理员登录
**功能描述**：管理员身份验证

**验证方式**：
- 管理员用户名+密码
- 会话管理
- 权限验证

### 3.2 商品管理

#### 3.2.1 商品列表
**展示内容**：
- 商品ID、名称、价格
- 分类、库存、状态
- 创建时间、操作按钮

**操作功能**：
- 搜索商品
- 编辑商品
- 删除商品
- 批量操作

#### 3.2.2 添加/编辑商品
**输入字段**：
- 商品名称（必填）
- 商品价格（必填，数字）
- 商品分类（必填，下拉选择）
- 商品描述（可选，富文本）
- 商品图片（可选，文件上传）
- 库存数量（必填，数字）

### 3.3 订单管理

#### 3.3.1 订单列表
**展示内容**：
- 订单编号、用户、金额
- 订单状态、创建时间
- 操作按钮

**操作功能**：
- 查看订单详情
- 更新订单状态
- 搜索订单

## 4. 用户界面设计要求

### 4.1 设计原则
- 简洁明了：界面简洁，信息层次清晰
- 易于操作：操作流程简单，减少用户学习成本
- 响应式设计：适配PC和移动端
- 一致性：保持整体风格统一

### 4.2 色彩方案
- 主色调：蓝色系（#007bff）
- 辅助色：灰色系（#6c757d）
- 成功色：绿色（#28a745）
- 警告色：橙色（#ffc107）
- 危险色：红色（#dc3545）

### 4.3 页面布局
- 头部：Logo、导航菜单、搜索框、用户信息
- 主体：内容区域
- 底部：版权信息、友情链接

## 5. 数据流程图

### 5.1 用户购物流程
```
用户注册/登录 → 浏览商品 → 添加到购物车 → 确认订单 → 提交订单 → 订单完成
```

### 5.2 商品管理流程
```
管理员登录 → 商品管理 → 添加/编辑商品 → 保存商品 → 商品上架
```

### 5.3 订单处理流程
```
用户提交订单 → 管理员查看订单 → 确认订单 → 发货 → 用户确认收货 → 订单完成
```

## 6. 接口设计要求

### 6.1 RESTful API设计
- 使用标准HTTP方法（GET、POST、PUT、DELETE）
- 统一的响应格式
- 适当的HTTP状态码
- API版本控制

### 6.2 数据格式
- 请求和响应使用JSON格式
- 时间格式使用ISO 8601标准
- 分页参数统一（page、size）

## 7. 性能要求

### 7.1 响应时间
- 页面加载时间 < 3秒
- API响应时间 < 1秒
- 数据库查询时间 < 500ms

### 7.2 并发处理
- 支持100个并发用户
- 数据库连接池管理
- 静态资源缓存

## 8. 安全要求

### 8.1 数据安全
- 密码加密存储
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

### 8.2 访问控制
- 用户会话管理
- 管理员权限验证
- 敏感操作日志记录

## 9. 测试要求

### 9.1 功能测试
- 用户注册登录测试
- 商品浏览和搜索测试
- 购物车和订单流程测试
- 后台管理功能测试

### 9.2 性能测试
- 负载测试
- 压力测试
- 数据库性能测试

### 9.3 安全测试
- 身份验证测试
- 权限控制测试
- 数据验证测试
