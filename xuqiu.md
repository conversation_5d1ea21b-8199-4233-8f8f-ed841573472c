# 项目一、AI辅助全流程开发企业数字化轻应用项目库

## 一、项目简介
本项目旨在利用AI大模型的代码生成与辅助能力，从零开始构建一个轻量级的在线商城系统，完整体验需求分析、产品设计、架构规划、原型制作、前后端开发、测试部署的现代化软件开发全流程。本项目为基础项目，无关联前置项目。

**项目主题：** 简易在线商城系统
**技术栈：** Flask + SQLAlchemy + MySQL + HTML/CSS/JavaScript + Bootstrap
**核心功能：** 用户注册登录、商品展示、购物车、订单管理、基础后台管理

## 二、核心模块
本项目的核心模块聚焦于AI赋能的全栈敏捷开发实践，以AI大模型作为智能开发伙伴，贯穿从需求分析与产品原型构思（AI辅助生成用户故事、功能模块建议），到轻量级前后端代码与数据库交互逻辑的快速构建（AI驱动生成HTML/CSS/JS骨架、API、SQL语句），再到AI辅助的自动化测试与文档初稿生成（AI辅助生成测试用例、功能描述与API文档）。整个过程强调利用AI高效迭代，实现一个可演示的在线网站系统原型，并通过记录和分析AI Prompts及其产出，量化AI在不同开发阶段的辅助效果。

## 三、职业技能
（1）掌握AI辅助开发：深入理解并熟练运用AI大模型进行代码生成、调试、文档撰写等。
（2）了解全栈开发技术：接触并实践前后端技术、数据库设计及API交互。
（3）熟悉项目完整流程：从需求分析到产品上线，完整体验软件工程的生命周期。
（4）掌握解决问题能力：学习如何将复杂需求拆解，并利用AI工具高效解决。
（5）熟悉产品规划设计：熟悉规划与设计一个完整的、可演示、可部署产品的过程。

## 四、项目资源
1、项目文档资源：提供不少于1套需求分析报告文档（用户故事、功能列表）、1套产品需求文档（PRD）（详细功能规格、流程图）、1套系统架构设计文档（技术选型、模块划分、部署图）、1套数据库设计文档（E-R图、表结构详情）、1套API接口文档（使用Swagger/OpenAPI规范）。
2、项目代码资源：提供不少于1套前端源代码（HTML，CSS，JavaScript）、1套后端源代码（Flask，包含API实现）、1套数据库脚本（SQL建表语句、示例数据插入脚本）。
3、项目素材资源：提供不少于1套UI设计稿/原型图（基于HTML的高保真原型）。


# 项目二、AI辅助的在线平台迭代升级与安全增强项目库

## 一、项目简介
本项目旨在在已有的轻量级网站基础上（项目一、AI辅助全流程开发企业数字化轻应用项目库开发成果），利用AI大模型的代码生成与辅助能力，进行迭代升级。学生将重点体验对现有系统进行需求分析、功能增强设计、安全加固（特别是高级登录验证）、前后端模块重构与新增、以及测试部署的现代化软件开发迭代流程。本项目将升级现有网站，使其具备更丰富的交互功能和更强的安全防护，为后续的爬虫项目提供一个具有挑战性的实战目标。

**项目主题：** 在线商城系统安全增强与功能扩展
**新增技术栈：** 验证码、Redis缓存、邮件服务、支付集成、数据分析
**核心升级：** 安全认证、高级功能、性能优化、用户体验提升

## 二、核心模块
本项目的核心模块聚焦于AI赋能的现有系统迭代与功能增强实践。以AI大模型作为智能开发伙伴，引导学生从分析现有网站架构与识别迭代需求（AI辅助分析现有代码瓶颈、提出新功能模块建议）开始，到针对性地设计并实现新增与重构模块（AI辅助设计并生成用户认证模块代码，特别是复杂登录流程；AI驱动生成新的API接口以支持动态内容加载，并进行数据库表结构扩展或优化SQL语句），再到AI辅助的集成测试与文档更新（AI辅助生成针对新功能的测试用例、更新原有功能描述与API文档）。整个过程强调利用AI高效迭代，将一个基础网站升级为一个功能更丰富、安全性更高的可演示在线系统原型，并通过记录和分析AI Prompts及其产出，量化AI在系统迭代与功能增强中的辅助效果。

## 三、职业技能
1、迭代需求分析报告文档: 用户故事，增强功能列表。
2、产品需求文档（PRD） (迭代版): 详细描述新增功能的规格、对现有功能的影响和修改。
3、系统架构设计文档 (迭代版): 技术选型（如引入新的库用于验证码），新增模块与现有模块的集成方式，更新后的部署图。
4、数据库设计文档 (迭代版): 更新的E-R图，新增或修改的表结构详情。
5、API接口文档 (迭代版): 新增或修改的API接口，使用Swagger/OpenAPI规范。

## 四、项目资源
1、前端源代码 (迭代版): 在原有HTML、CSS、JavaScript基础上，新增或修改的页面和脚本（如登录页的复杂验证逻辑等）。
2、后端源代码 (迭代版): 在原有后端的基础上，新增或修改的API实现（用户认证逻辑等）。
3、数据库脚本 (迭代版): 针对数据库修改的SQL迁移脚本（ALTER TABLE等）、新增表的CREATE TABLE语句、新功能的示例数据插入脚本。
4、UI设计稿/原型图 (针对新增功能): 基于HTML的高保真原型。