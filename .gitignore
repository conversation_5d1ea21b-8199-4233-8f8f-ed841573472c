*.py[cod]

# C extensions
*.so

# Packages
*.egg
*.egg-info
eggs
parts
bin
var
sdist
develop-eggs
.installed.cfg
lib
lib64

# Installer logs
pip-log.txt

# Unit test / coverage reports
.coverage
.tox
nosetests.xml
.pytest_cache

# Mr Developer
.mr.developer.cfg
.project
.pydevproject

# Complexity
output/*.html
output/*/index.html

# Sphinx
docs/_build

.webassets-cache

# Virtualenvs
env/

# npm
node_modules

# mpvue build files
/mpvue/dist/

/flaskshop/local_config.py
slow_queries.log
migrations/
.idea/
.venv/
.vscode/
/venv/
/women_shop_empty.backup
/.flaskenv
